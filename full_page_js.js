function a(){const aT=['slice','charCodeAt','length','random','toString','concat','substring','ungzip','Data\x20decompression\x20error:','timeZone','【考试说明】请使用抓包软件（如Wireshark）或浏览器F12开发者工具查看网络请求，分析并解密传输数据，将解密结果填入下方答题区域','重新考试','🔍\x20或使用Wireshark等专业抓包工具分析网络流量','•\x20数据解密能力评估','•\x20协议分析实战操作','•\x20解密数据必须为有效的JSON格式','正确解密并提交请求数据和响应数据的JSON格式内容，所有必需字段完整且格式正确即可通过考试。通过考试后将获得LinuxDo社区颁发的网络调试与数据解密技能认证。','This\x20is\x20a\x20technical\x20ability\x20test\x20designed\x20to\x20assess\x20your\x20network\x20debugging\x20and\x20data\x20decryption\x20skills.\x20You\x20need\x20to\x20use\x20technical\x20means\x20to\x20obtain\x20and\x20decrypt\x20network\x20transmitted\x20data.','This\x20exam\x20tests\x20your\x20Next.js\x20project\x20reverse\x20analysis\x20capabilities,\x20network\x20protocol\x20understanding,\x20encryption\x20algorithm\x20reverse\x20engineering\x20skills,\x20and\x20data\x20decryption\x20&\x20restoration\x20abilities','Response\x20data\x20decryption\x20result\x20(JSON\x20format):','Please\x20fill\x20in\x20complete\x20decrypted\x20data','Exam\x20Subjects','•\x20No\x20time\x20limit,\x20can\x20retry\x20multiple\x20times','hours','header','jsxs','bg-[#da7756]\x20bg-opacity-10\x20border\x20border-[#da7756]\x20border-opacity-30\x20rounded-lg\x20p-4\x20mb-6\x20max-w-4xl\x20mx-auto','examSubtitle','jsx','section','div','text-xl\x20font-bold\x20mb-4\x20text-center','instructionText','target','label','submitVerification','button','text-lg\x20font-bold\x20text-[#da7756]\x20mb-4\x20flex\x20items-center','text-red-700\x20flex\x20items-start','space-y-1\x20text-sm','第二题（响应数据）问题：','map','span','bg-[#3d3929]\x20bg-opacity-5\x20border\x20border-[#3d3929]\x20border-opacity-20\x20rounded-lg\x20p-3','bg-green-100\x20text-green-800\x20border\x20border-green-300','flex\x20items-center\x20justify-center\x20mb-2','text-2xl\x20mr-2','hints','mb-12','tip1','footer','max-w-4xl\x20mx-auto\x20text-center','application/ld+json','EducationalOrganization','网络调试能力测试','LinuxDo社区','Question','安全研究员：测试数据解密和逆向分析技能','理解网络请求和响应流程','考试流程','📋\x20','grid\x20md:grid-cols-2\x20gap-6\x20text-sm','subjectItem3','bg-white\x20bg-opacity-30\x20p-4\x20rounded-lg','font-semibold\x20text-[#3d3929]\x20mb-3\x20flex\x20items-center','toolItem3','text-sm\x20text-[#3d3929]\x20opacity-80\x20space-y-2','passingStandard','text-sm\x20text-[#3d3929]\x20opacity-80\x20leading-relaxed','text-xs\x20text-[#3d3929]\x20opacity-60\x20mt-1\x20font-normal','useState','obfuscationMarker','join','stringify','main'];a=function(){return aT;};return a();}function b(c,d){const e=a();b=function(f,g){f=f-0x0;let h=e[f];return h;};return b(c,d);}(self['webpackChunk_N_E']=self['webpackChunk_N_E']||[])['push']([[0x3a3],{0x23b0:function(c,d,f){Promise['resolve']()['then'](f['bind'](f,0xa03));},0xeea:function(f,j,k){'use strict';k['d'](j,{'el':function(){return H;},'mu':function(){return J;},'xi':function(){return I;}});var q=k(0xd61);let v={'keyFragments':['Folda','Scan','2024','Secure','Key','!@#','$%^','&*(',')'],'headerParts':['----','UNS','----'],'saltGenerator':()=>'ENTERPRISE_SECURITY_LAYER'+Math['floor'](Date['now']()/0x1499700)['toString'](0x24),'envFingerprint':btoa(navigator['userAgent']['slice'](0x0,0x14)),'getUtcTimestamp':()=>Date['now'](),'getUtcDayIndex':()=>Math['floor'](Date['now']()/0x5265c00),'getUtcHourIndex':()=>Math['floor'](Date['now']()/0x36ee80)},w=()=>{const at=b;let K=v['keyFragments'],L=v['saltGenerator'](),M=v['envFingerprint'];return(K['slice'](0x0,0x4)['join']('')+K['slice'](0x4,0x8)['join']('')+K[0x8]+L['slice'](-0x8)+M[at(0x0)](-0x6))['split']('')['map']((N,O)=>String['fromCharCode'](N[at(0x1)](0x0)+(O%0x3+0x1)))['join']('');},y=()=>{let K=v['headerParts'],L=(v['getUtcDayIndex']()%0x7)['toString']();return K[0x0]+K[0x1]+L+K[0x2];},z=null,A=null,B=0x0,C=-0x1,D=()=>{let K=v['getUtcTimestamp']();return(!z||K-B>0x1499700)&&(z=w(),B=K),z;},E=()=>{let K=v['getUtcDayIndex']();return A&&C===K||(A=y(),C=K),A;};function F(K,L){const au=b;let M=new Uint8Array(K['length']),N=new TextEncoder()['encode'](L);for(let O=0x0;O<K['length'];O++)M[O]=K[O]^N[O%N[au(0x2)]];return M;}function G(){const av=b;for(var K=arguments['length'],L=Array(K),M=0x0;M<K;M++)L[M]=arguments[M];let N=new Uint8Array(L['reduce']((P,Q)=>P+Q[av(0x2)],0x0)),O=0x0;for(let P of L)N['set'](P,O),O+=P['length'];return N;}function H(K){try{let L=JSON['stringify'](K),M=q['ZP']['gzip'](L),N=(function(){const aw=b;let S=v['getUtcTimestamp'](),T=Math[aw(0x3)]()[aw(0x4)](0x24)['substring'](0x2),U=v['envFingerprint']['slice'](-0x4),V=v['saltGenerator']()['slice'](-0x6),W=[''[aw(0x5)](S,'_')[aw(0x5)](T,'_NOISE_DATA_')[aw(0x5)](Math['floor'](0x2710*Math['random']())),'FAKE_API_KEY_'['concat'](U,'_')['concat'](V,'_')[aw(0x5)](S['toString'](0x24)),'DECOY_TOKEN_'['concat'](Math['random']()['toString'](0x10)[aw(0x6)](0x2),'_END'),'PHANTOM_HASH_'['concat'](btoa(T)['slice'](0x0,0x8),'_TRAIL'),'UTC_SYNC_'[aw(0x5)](S,'_')[aw(0x5)](U,'_GLOBAL')],X=W[Math['floor'](Math[aw(0x3)]()*W['length'])],Y='X'['repeat'](Math['floor'](0x32*Math['random']())+0xa);return new TextEncoder()['encode'](X+Y);}()),O=G(M,N),P=F(O,D()),Q=new TextEncoder()['encode'](E()),R=G(Q,P);return btoa(Array['from'](R,S=>String['fromCharCode'](S))['join'](''));}catch(S){throw console['error']('Data\x20compression\x20error:',S),Error('Failed\x20to\x20compress\x20data');}}function I(K){const ax=b;try{let L=atob(K),M=new Uint8Array(L['length']);for(let R=0x0;R<L['length'];R++)M[R]=L['charCodeAt'](R);let N=new TextEncoder()['encode'](E());if(M['length']<N['length'])throw Error('Invalid\x20data\x20format:\x20too\x20short');for(let S=0x0;S<N['length'];S++)if(M[S]!==N[S])throw Error('Invalid\x20data\x20format:\x20missing\x20header\x20signature');let O=M[ax(0x0)](N['length']),P=F(O,D()),Q=null;for(let T of[()=>{for(let U=0xa;U<P['length']-0x32;U++)try{let V=P['slice'](0x0,U),W=q['ZP']['ungzip'](V,{'to':'string'});return JSON['parse'](W),W;}catch(X){continue;}return null;},()=>{const ay=b;for(let U=P['length']-0x64;U>0x32;U--)try{let V=P['slice'](0x0,U),W=q['ZP'][ay(0x7)](V,{'to':'string'});return JSON['parse'](W),W;}catch(X){continue;}return null;}])if(Q=T())break;if(null===Q)throw Error('Failed\x20to\x20extract\x20original\x20data\x20from\x20obfuscated\x20format');return JSON['parse'](Q);}catch(U){throw console['error'](ax(0x8),U),Error('Failed\x20to\x20decompress\x20data');}}function J(){const az=b;let K=v['getUtcTimestamp'](),L=v['getUtcDayIndex']();return{'utcTimestamp':K,'utcDayIndex':L,'utcHourIndex':v['getUtcHourIndex'](),'currentHeaderSignature':E(),'localTimezone':Intl['DateTimeFormat']()['resolvedOptions']()[az(0x9)],'utcDateString':new Date(K)['toISOString'](),'localDateString':new Date(K)['toString'](),'keyRotationInfo':{'nextKeyRotationIn':0x1499700*Math['ceil'](K/0x1499700)-K,'nextHeaderChangeIn':(L+0x1)*0x5265c00-K}};}},0xa03:function(k,q,w){'use strict';const aB=b;w['r'](q),w['d'](q,{'default':function(){return V;}});var z=w(0x1d0d),A=w(0x8d9),B=w(0xd61);let C={'staticKey':'TechExam2024_EncryptionKey_ForTesting','headerSignature':'----TECH_EXAM_2024----','obfuscationMarker':'___OBFUSCATION_DATA___'};function E(){const aA=b;for(var W=arguments['length'],X=Array(W),Y=0x0;Y<W;Y++)X[Y]=arguments[Y];let Z=new Uint8Array(X['reduce']((a1,a2)=>a1+a2[aA(0x2)],0x0)),a0=0x0;for(let a1 of X)Z['set'](a1,a0),a0+=a1['length'];return Z;}let F={'zh':{'pageTitle':'LinuxDo社区高考','pageDescription':'LinuxDo社区技术高考，专业测试网络调试和数据解密能力。通过抓包分析、加密解密等技术手段，检验您的网络安全技能水平。','techChallenge':'网络调试与数据解密能力考试','challengeDescription':'欢迎参加LinuxDo社区技术高考！本次考试将全面检验您的网络调试和数据解密能力。考试采用实战模式，需要您运用专业技术手段获取并解密网络传输数据。','examSubtitle':'本次考试考验您的Next.js项目逆向分析能力、网络协议理解能力、加密算法逆向工程技能以及数据解密与还原能力','startTest':'开始考试','startingTest':'正在生成考试题目...','decryptionChallenge':'考试题目：网络数据解密分析','instructionText':aB(0xa),'requestDataLabel':'第一题：请求数据解密结果（JSON格式）','responseDataLabel':'第二题：响应数据解密结果（JSON格式）','requestPlaceholder':'请在此处填写解密后的请求数据JSON...','responsePlaceholder':'请在此处填写解密后的响应数据JSON...','submitVerification':'提交答卷','verifying':'正在阅卷...','startOver':aB(0xb),'verificationSuccess':'🎉\x20恭喜！考试通过！您已成功掌握网络调试和数据解密技能！','verificationFailed':'❌\x20考试未通过，请仔细检查解密数据的准确性后重新提交','verificationError':'系统错误，请重新提交答卷','technicalTips':'考试技巧与提示','tip1':'📋\x20使用浏览器F12开发者工具的Network标签页查看网络请求','tip2':aB(0xc),'tip3':'🔐\x20数据经过了gzip压缩\x20+\x20XOR加密\x20+\x20头部标识等多重保护','tip4':'🧠\x20需要理解加密算法并正确解密数据','tip5':'✅\x20解密后的数据应该是有效的JSON格式','networkError':'网络连接失败，请检查网络后重试','fillAllFields':'请完整填写所有答题区域','footerText':'LinuxDo社区高考','examInstructions':'考试须知','examSubject':'考试科目','examTime':'考试时间','requiredTools':'必备工具','gradingCriteria':'评分标准','importantReminder':'重要提醒','passingStandard':'通过标准','subjectItem1':'•\x20网络调试技能测试','subjectItem2':aB(0xd),'subjectItem3':aB(0xe),'timeItem1':'•\x20不限时间，可反复尝试','timeItem2':'•\x20建议预留30-60分钟','timeItem3':'•\x20支持中途暂停和继续','toolItem1':'•\x20浏览器F12开发者工具','toolItem2':'•\x20Wireshark抓包软件（可选）','toolItem3':'•\x20JSON格式化工具（可选）','gradeItem1':'•\x20数据解密准确性：50%','gradeItem2':'•\x20JSON格式正确性：30%','gradeItem3':'•\x20字段完整性：20%','reminderItem1':'•\x20请确保网络连接稳定','reminderItem2':'•\x20考试过程中请勿刷新页面','reminderItem3':aB(0xf),'reminderItem4':'•\x20所有字段名和值都区分大小写','reminderItem5':'•\x20时间戳等动态字段需要使用实际值','passingDescription':aB(0x10),'keyRefreshCountdown':'密钥刷新倒计时','keyRefreshIn':'距离下次密钥刷新还有','hours':'小时','minutes':'分钟','seconds':'秒','keyRefreshDescription':'系统每6小时自动刷新加密密钥，确保数据传输安全性。所有用户的密钥刷新时间完全同步。'},'en':{'pageTitle':'Community\x20Technical\x20Ability\x20Test','pageDescription':aB(0x11),'techChallenge':'Technical\x20Challenge\x20Test','challengeDescription':'This\x20is\x20a\x20technical\x20ability\x20test\x20designed\x20to\x20assess\x20your\x20network\x20debugging\x20and\x20data\x20decryption\x20skills.\x20You\x20need\x20to\x20use\x20technical\x20means\x20to\x20obtain\x20and\x20decrypt\x20network\x20transmitted\x20data.','examSubtitle':aB(0x12),'startTest':'Start\x20Technical\x20Test','startingTest':'Sending\x20test\x20request...','decryptionChallenge':'Decryption\x20Challenge','instructionText':'Please\x20use\x20packet\x20capture\x20software\x20(such\x20as\x20Wireshark)\x20or\x20browser\x20F12\x20developer\x20tools\x20to\x20view\x20network\x20requests,\x20then\x20decrypt\x20the\x20data\x20and\x20fill\x20in\x20the\x20input\x20boxes\x20below','requestDataLabel':'Request\x20data\x20decryption\x20result\x20(JSON\x20format):','responseDataLabel':aB(0x13),'requestPlaceholder':'Please\x20enter\x20the\x20decrypted\x20request\x20data...','responsePlaceholder':'Please\x20enter\x20the\x20decrypted\x20response\x20data...','submitVerification':'Submit\x20Verification','verifying':'Verifying...','startOver':'Start\x20Over','verificationSuccess':'Verification\x20successful!\x20You\x20passed\x20the\x20technical\x20test!','verificationFailed':'Verification\x20failed,\x20please\x20check\x20your\x20decrypted\x20data','verificationError':'Verification\x20process\x20error,\x20please\x20try\x20again','technicalTips':'Technical\x20Tips','tip1':'•\x20Use\x20browser\x20F12\x20developer\x20tools\x20Network\x20tab\x20to\x20view\x20network\x20requests','tip2':'•\x20Or\x20use\x20professional\x20packet\x20capture\x20tools\x20like\x20Wireshark\x20to\x20analyze\x20network\x20traffic','tip3':'•\x20Data\x20has\x20been\x20protected\x20by\x20gzip\x20compression\x20+\x20XOR\x20encryption\x20+\x20header\x20signature','tip4':'•\x20Need\x20to\x20understand\x20encryption\x20algorithms\x20and\x20correctly\x20decrypt\x20data','tip5':'•\x20Decrypted\x20data\x20should\x20be\x20in\x20valid\x20JSON\x20format','networkError':'Network\x20request\x20failed,\x20please\x20try\x20again','fillAllFields':aB(0x14),'footerText':'Technical\x20Ability\x20Test\x20Platform','examInstructions':'Exam\x20Instructions','examSubject':aB(0x15),'examTime':'Exam\x20Duration','requiredTools':'Required\x20Tools','gradingCriteria':'Grading\x20Criteria','importantReminder':'Important\x20Reminders','passingStandard':'Passing\x20Standard','subjectItem1':'•\x20Network\x20debugging\x20skills\x20test','subjectItem2':'•\x20Data\x20decryption\x20ability\x20assessment','subjectItem3':'•\x20Protocol\x20analysis\x20practical\x20operation','timeItem1':aB(0x16),'timeItem2':'•\x20Recommended\x2030-60\x20minutes','timeItem3':'•\x20Support\x20pause\x20and\x20resume','toolItem1':'•\x20Browser\x20F12\x20developer\x20tools','toolItem2':'•\x20Wireshark\x20packet\x20capture\x20software\x20(optional)','toolItem3':'•\x20JSON\x20formatting\x20tools\x20(optional)','gradeItem1':'•\x20Data\x20decryption\x20accuracy:\x2050%','gradeItem2':'•\x20JSON\x20format\x20correctness:\x2030%','gradeItem3':'•\x20Field\x20completeness:\x2020%','reminderItem1':'•\x20Ensure\x20stable\x20network\x20connection','reminderItem2':'•\x20Do\x20not\x20refresh\x20the\x20page\x20during\x20exam','reminderItem3':'•\x20Decrypted\x20data\x20must\x20be\x20valid\x20JSON\x20format','reminderItem4':'\u2022 All field names and values are case-sensitive','reminderItem5':'\u2022 Dynamic fields like timestamps need actual values','passingDescription':'Correctly\x20decrypt\x20and\x20submit\x20JSON\x20format\x20content\x20of\x20request\x20and\x20response\x20data.\x20All\x20required\x20fields\x20must\x20be\x20complete\x20and\x20correctly\x20formatted\x20to\x20pass\x20the\x20exam.\x20Upon\x20passing,\x20you\x20will\x20receive\x20a\x20network\x20debugging\x20and\x20data\x20decryption\x20skill\x20certification\x20from\x20LinuxDo\x20community.','keyRefreshCountdown':'Key\x20Refresh\x20Countdown','keyRefreshIn':'Next\x20key\x20refresh\x20in','hours':aB(0x17),'minutes':'minutes','seconds':'seconds','keyRefreshDescription':'The system automatically refreshes encryption keys every 6 hours to ensure data transmission security. All users\' key refresh times are fully synchronized.'}};function G(W){return F[W||'zh']||F['zh'];}function H(W){const aC=b;let {language:X}=W,Y=G(X);return(0x0,z['jsx'])(aC(0x18),{'className':'py-6\x20px-4\x20sm:px-8\x20md:px-16','children':(0x0,z['jsx'])('div',{'className':'max-w-4xl\x20mx-auto','children':(0x0,z['jsx'])('h1',{'className':'text-2xl\x20font-bold','children':Y['pageTitle']})})});}function I(W){const aD=b;let {language:X}=W,Y=G(X);return(0x0,z[aD(0x19)])('section',{'className':'mb-12\x20text-center','children':[(0x0,z['jsx'])('h2',{'className':'text-3xl\x20sm:text-4xl\x20font-bold\x20mb-4','children':Y['techChallenge']}),(0x0,z['jsxs'])('div',{'className':aD(0x1a),'children':[(0x0,z['jsx'])('p',{'className':'text-sm\x20font-medium\x20text-[#da7756]\x20mb-2','children':'📋\x20考试范围\x20|\x20Exam\x20Scope'}),(0x0,z['jsx'])('p',{'className':'text-sm\x20text-[#3d3929]\x20opacity-90\x20leading-relaxed','children':Y[aD(0x1b)]})]}),(0x0,z['jsx'])('p',{'className':'text-lg\x20opacity-80\x20max-w-2xl\x20mx-auto\x20mb-8','children':Y['challengeDescription']})]});}function J(W){const aE=b;let {isLoading:X,onStartTest:Y,language:Z}=W,a0=G(Z);return(0x0,z['jsx'])('section',{'className':'text-center\x20mb-12','children':(0x0,z[aE(0x1c)])('button',{'onClick':Y,'disabled':X,'className':'bg-[#da7756]\x20hover:bg-opacity-90\x20disabled:opacity-50\x20text-white\x20px-8\x20py-4\x20rounded-lg\x20text-lg\x20font-semibold\x20transition-colors\x20shadow-lg','children':X?a0['startingTest']:a0['startTest']})});}function K(W){const aF=b;let {requestInput:X,responseInput:Y,isLoading:Z,onRequestInputChange:a0,onResponseInputChange:a1,onSubmitVerification:a2,onReset:a3,language:a4}=W,a5=G(a4);return(0x0,z['jsx'])(aF(0x1d),{'className':'mb-12','children':(0x0,z[aF(0x19)])(aF(0x1e),{'className':'bg-white\x20bg-opacity-50\x20p-6\x20rounded-lg\x20shadow-sm\x20border\x20border-[#3d3929]\x20border-opacity-10\x20mb-6','children':[(0x0,z['jsx'])('h3',{'className':aF(0x1f),'children':a5['decryptionChallenge']}),(0x0,z['jsx'])('p',{'className':'text-center\x20mb-6\x20text-sm\x20opacity-80','children':a5[aF(0x20)]}),(0x0,z[aF(0x19)])('div',{'className':'space-y-4','children':[(0x0,z['jsxs'])('div',{'children':[(0x0,z['jsx'])('label',{'className':'block\x20text-sm\x20font-medium\x20mb-2','children':a5['requestDataLabel']}),(0x0,z['jsx'])('textarea',{'value':X,'onChange':a6=>a0(a6[aF(0x21)]['value']),'placeholder':a5['requestPlaceholder'],'className':'w-full\x20p-3\x20border\x20border-gray-300\x20rounded-md\x20h-24\x20text-sm'})]}),(0x0,z['jsxs'])('div',{'children':[(0x0,z['jsx'])(aF(0x22),{'className':'block\x20text-sm\x20font-medium\x20mb-2','children':a5['responseDataLabel']}),(0x0,z['jsx'])('textarea',{'value':Y,'onChange':a6=>a1(a6['target']['value']),'placeholder':a5['responsePlaceholder'],'className':'w-full\x20p-3\x20border\x20border-gray-300\x20rounded-md\x20h-24\x20text-sm'})]})]}),(0x0,z['jsxs'])('div',{'className':'flex\x20justify-center\x20space-x-4\x20mt-6','children':[(0x0,z[aF(0x1c)])('button',{'onClick':a2,'disabled':Z,'className':'bg-[#da7756]\x20hover:bg-opacity-90\x20disabled:opacity-50\x20text-white\x20px-6\x20py-2\x20rounded-md\x20transition-colors','children':Z?a5['verifying']:a5[aF(0x23)]}),(0x0,z['jsx'])(aF(0x24),{'onClick':a3,'className':'bg-gray-500\x20hover:bg-opacity-90\x20text-white\x20px-6\x20py-2\x20rounded-md\x20transition-colors','children':a5['startOver']})]})]})});}function L(W){const aG=b;let {errors:X,requestErrors:Y=[],responseErrors:Z=[],hints:a0=[],language:a1}=W;return(G(a1),0x0===X['length']&&0x0===Y[aG(0x2)]&&0x0===Z['length'])?null:(0x0,z['jsxs'])('div',{'className':'bg-white\x20bg-opacity-50\x20p-6\x20rounded-lg\x20shadow-sm\x20border\x20border-[#da7756]\x20border-opacity-30\x20mb-6','children':[(0x0,z['jsx'])('h4',{'className':aG(0x25),'children':'📋\x20考试结果详情'}),X['length']>0x0&&(0x0,z['jsxs'])('div',{'className':'mb-4','children':[(0x0,z['jsx'])('h5',{'className':'font-semibold\x20text-[#3d3929]\x20mb-2','children':'总体问题：'}),(0x0,z['jsx'])(aG(0x1e),{'className':'bg-red-50\x20border\x20border-red-200\x20rounded-lg\x20p-3','children':(0x0,z[aG(0x1c)])('ul',{'className':'space-y-1\x20text-sm','children':X['map']((a2,a3)=>(0x0,z['jsxs'])('li',{'className':aG(0x26),'children':[(0x0,z['jsx'])('span',{'className':'mr-2\x20mt-0.5','children':'•'}),(0x0,z['jsx'])('span',{'children':a2})]},a3))})})]}),Y['length']>0x0&&(0x0,z['jsxs'])('div',{'className':'mb-4','children':[(0x0,z['jsx'])('h5',{'className':'font-semibold\x20text-[#3d3929]\x20mb-2','children':'第一题（请求数据）问题：'}),(0x0,z['jsx'])(aG(0x1e),{'className':'bg-orange-50\x20border\x20border-orange-200\x20rounded-lg\x20p-3','children':(0x0,z['jsx'])('ul',{'className':aG(0x27),'children':Y['map']((a2,a3)=>(0x0,z['jsxs'])('li',{'className':'text-orange-700\x20flex\x20items-start','children':[(0x0,z['jsx'])('span',{'className':'mr-2\x20mt-0.5','children':'•'}),(0x0,z['jsx'])('span',{'children':a2})]},a3))})})]}),Z['length']>0x0&&(0x0,z['jsxs'])('div',{'className':'mb-4','children':[(0x0,z[aG(0x1c)])('h5',{'className':'font-semibold\x20text-[#3d3929]\x20mb-2','children':aG(0x28)}),(0x0,z['jsx'])('div',{'className':'bg-red-50\x20border\x20border-red-200\x20rounded-lg\x20p-3','children':(0x0,z[aG(0x1c)])('ul',{'className':'space-y-1\x20text-sm','children':Z[aG(0x29)]((a2,a3)=>(0x0,z['jsxs'])('li',{'className':'text-red-700\x20flex\x20items-start','children':[(0x0,z['jsx'])('span',{'className':'mr-2\x20mt-0.5','children':'•'}),(0x0,z['jsx'])(aG(0x2a),{'children':a2})]},a3))})})]}),a0[aG(0x2)]>0x0&&(0x0,z['jsxs'])('div',{'className':'mb-4','children':[(0x0,z['jsx'])('h5',{'className':'font-semibold\x20text-[#3d3929]\x20mb-2','children':'💡\x20解题提示：'}),(0x0,z['jsx'])('div',{'className':'bg-blue-50\x20border\x20border-blue-200\x20rounded-lg\x20p-3','children':(0x0,z['jsx'])('ul',{'className':'space-y-1\x20text-sm','children':a0['map']((a2,a3)=>(0x0,z[aG(0x19)])('li',{'className':'text-blue-700\x20flex\x20items-start','children':[(0x0,z['jsx'])(aG(0x2a),{'className':'mr-2\x20mt-0.5','children':'•'}),(0x0,z['jsx'])('span',{'children':a2})]},a3))})})]}),(0x0,z[aG(0x19)])('div',{'className':aG(0x2b),'children':[(0x0,z['jsx'])('h5',{'className':'font-semibold\x20text-[#3d3929]\x20mb-2','children':'🔧\x20调试建议：'}),(0x0,z['jsxs'])('ul',{'className':'space-y-1\x20text-sm\x20text-[#3d3929]\x20opacity-80','children':[(0x0,z['jsx'])('li',{'children':'•\x20使用浏览器F12开发者工具查看Network标签页中的实际请求和响应'}),(0x0,z['jsx'])('li',{'children':'•\x20检查JSON格式是否正确（可使用在线JSON验证工具）'}),(0x0,z['jsx'])('li',{'children':'•\x20确保所有字段名和值都完全匹配（注意大小写和标点符号）'}),(0x0,z['jsx'])('li',{'children':'•\x20时间戳等动态字段需要使用从网络请求中获取的实际值'}),(0x0,z['jsx'])('li',{'children':'•\x20如需帮助，可查看控制台日志获取更多调试信息'})]})]})]});}function M(W){const aH=b;var X;let {result:Y,verificationData:Z,language:a0}=W;G(a0);let a1=(null==Z?void 0x0:Z['success'])||Y['includes']('成功')||Y['includes']('successful');return(0x0,z['jsxs'])(aH(0x1d),{'className':'mb-12','children':[(0x0,z['jsx'])('div',{'className':'text-center\x20mb-6','children':(0x0,z['jsxs'])('div',{'className':'p-6\x20rounded-lg\x20'['concat'](a1?aH(0x2c):'bg-red-100\x20text-red-800\x20border\x20border-red-300'),'children':[(0x0,z[aH(0x19)])(aH(0x1e),{'className':aH(0x2d),'children':[(0x0,z['jsx'])('span',{'className':aH(0x2e),'children':a1?'🎉':'❌'}),(0x0,z['jsx'])('h3',{'className':'text-xl\x20font-bold','children':a1?'考试通过！':'考试未通过'})]}),(0x0,z['jsx'])('p',{'className':'font-semibold\x20text-lg','children':(null==Z?void 0x0:Z['message'])||Y}),a1&&(null==Z?void 0x0:Z['details'])&&(0x0,z['jsxs'])('div',{'className':'mt-4\x20p-4\x20bg-white\x20bg-opacity-50\x20rounded-lg','children':[(0x0,z['jsx'])('h4',{'className':'font-semibold\x20mb-2','children':'🏆\x20技能认证'}),(0x0,z['jsx'])('p',{'className':'text-sm\x20mb-2','children':'您已成功掌握以下技能：'}),(0x0,z['jsx'])('div',{'className':'flex\x20flex-wrap\x20justify-center\x20gap-2','children':null===(X=Z['details']['skills_demonstrated'])||void 0x0===X?void 0x0:X['map']((a2,a3)=>(0x0,z['jsx'])('span',{'className':'bg-green-200\x20text-green-800\x20px-2\x20py-1\x20rounded-full\x20text-xs','children':a2},a3))})]})]})}),!a1&&Z&&(0x0,z['jsx'])(L,{'errors':Z['errors']||[],'requestErrors':Z['request_errors'],'responseErrors':Z['response_errors'],'hints':Z[aH(0x2f)],'language':a0})]});}function O(W){const aI=b;let {language:X}=W,Y=G(X);return(0x0,z['jsx'])(aI(0x1d),{'className':aI(0x30),'children':(0x0,z['jsxs'])('div',{'className':'bg-white\x20bg-opacity-30\x20p-6\x20rounded-lg','children':[(0x0,z['jsx'])('h3',{'className':'text-xl\x20font-bold\x20mb-4','children':Y['technicalTips']}),(0x0,z['jsxs'])('ul',{'className':'space-y-2\x20text-sm\x20opacity-80','children':[(0x0,z['jsx'])('li',{'children':Y[aI(0x31)]}),(0x0,z['jsx'])('li',{'children':Y['tip2']}),(0x0,z[aI(0x1c)])('li',{'children':Y['tip3']}),(0x0,z['jsx'])('li',{'children':Y['tip4']}),(0x0,z[aI(0x1c)])('li',{'children':Y['tip5']})]})]})});}function P(W){const aJ=b;let {language:X}=W,Y=G(X);return(0x0,z[aJ(0x1c)])(aJ(0x32),{'className':'py-8\x20px-4\x20sm:px-8\x20md:px-16\x20border-t\x20border-[#3d3929]\x20border-opacity-10','children':(0x0,z['jsx'])('div',{'className':aJ(0x33),'children':(0x0,z['jsx'])('p',{'className':'text-sm\x20opacity-60','children':Y['footerText']})})});}function Q(W){const aK=b;let {currentLanguage:X,onLanguageChange:Y}=W;return(0x0,z[aK(0x1c)])('div',{'className':'fixed\x20top-4\x20right-4\x20z-50','children':(0x0,z['jsxs'])(aK(0x1e),{'className':'bg-white\x20bg-opacity-90\x20rounded-lg\x20shadow-md\x20p-2\x20flex\x20space-x-2','children':[(0x0,z['jsx'])('button',{'onClick':()=>Y('zh'),'className':'px-3\x20py-1\x20rounded\x20text-sm\x20font-medium\x20transition-colors\x20'['concat']('zh'===X?'bg-[#da7756]\x20text-white':'text-[#3d3929]\x20hover:bg-gray-100'),'children':'中文'}),(0x0,z['jsx'])('button',{'onClick':()=>Y('en'),'className':'px-3\x20py-1\x20rounded\x20text-sm\x20font-medium\x20transition-colors\x20'['concat']('en'===X?'bg-[#da7756]\x20text-white':'text-[#3d3929]\x20hover:bg-gray-100'),'children':'English'})]})});}function R(W){const aL=b;let {language:X}=W;return G(X),(0x0,z['jsxs'])(z['Fragment'],{'children':[(0x0,z[aL(0x1c)])('script',{'type':aL(0x34),'dangerouslySetInnerHTML':{'__html':JSON['stringify']({'@context':'https://schema.org','@type':aL(0x35),'name':'LinuxDo社区高考','description':'专业的网络调试和数据解密能力测试平台','url':'https://exam.linuxdo.org','sameAs':['https://linux.do','https://github.com/linuxdo-org'],'educationalCredentialAwarded':'网络调试与数据解密技能认证','hasOfferCatalog':{'@type':'OfferCatalog','name':'技术能力测试','itemListElement':[{'@type':'Course','name':aL(0x36),'description':'通过抓包分析测试网络调试技能','provider':{'@type':'Organization','name':'LinuxDo社区'}},{'@type':'Course','name':'数据解密能力测试','description':'通过加密数据解密测试安全技能','provider':{'@type':'Organization','name':aL(0x37)}}]}})}}),(0x0,z['jsx'])('script',{'type':'application/ld+json','dangerouslySetInnerHTML':{'__html':JSON['stringify']({'@context':'https://schema.org','@type':'FAQPage','mainEntity':[{'@type':aL(0x38),'name':'LinuxDo社区高考是什么？','acceptedAnswer':{'@type':'Answer','text':'LinuxDo社区高考是一个专业的技术能力测试平台，主要测试网络调试和数据解密能力。通过实战模式的考试，检验参与者的网络安全技能水平。'}},{'@type':aL(0x38),'name':'考试需要什么工具？','acceptedAnswer':{'@type':'Answer','text':'考试需要使用网络调试工具，如浏览器F12开发者工具或Wireshark抓包软件，用于分析网络请求和解密传输数据。'}},{'@type':aL(0x38),'name':'适合什么人参加？','acceptedAnswer':{'@type':'Answer','text':'适合程序员、网络工程师、安全研究员、运维工程师等技术人员参加，用于检验和提升网络调试与数据解密技能。'}},{'@type':'Question','name':'考试难度如何？','acceptedAnswer':{'@type':'Answer','text':'考试采用高级难度，需要掌握网络协议分析、加密解密算法、抓包分析等专业技能。适合有一定技术基础的人员挑战。'}}]})}}),(0x0,z['jsxs'])(aL(0x1e),{'className':'sr-only','children':[(0x0,z['jsx'])('h1',{'children':'LinuxDo社区高考\x20-\x20网络调试和数据解密能力测试'}),(0x0,z['jsx'])('h2',{'children':'专业的技术能力评估平台'}),(0x0,z['jsx'])('p',{'children':'LinuxDo社区技术高考是一个专业的网络技术能力测试平台，专注于评估参与者的网络调试和数据解密技能。\x20我们的考试采用实战模式，要求考生运用专业的技术手段，如Wireshark抓包分析、浏览器F12开发者工具等，\x20来获取并解密网络传输的数据。'}),(0x0,z[aL(0x1c)])('h3',{'children':'考试特色'}),(0x0,z['jsxs'])('ul',{'children':[(0x0,z['jsx'])('li',{'children':'实战导向：真实的网络环境和加密数据'}),(0x0,z['jsx'])('li',{'children':'技能全面：涵盖网络调试、数据解密、协议分析'}),(0x0,z['jsx'])('li',{'children':'难度适中：适合有技术基础的程序员和工程师'}),(0x0,z['jsx'])('li',{'children':'即时反馈：自动化评分系统，即时获得结果'})]}),(0x0,z['jsx'])('h3',{'children':'适用人群'}),(0x0,z['jsxs'])('ul',{'children':[(0x0,z['jsx'])('li',{'children':'程序员：提升网络调试和安全技能'}),(0x0,z['jsx'])('li',{'children':'网络工程师：验证网络协议分析能力'}),(0x0,z['jsx'])('li',{'children':aL(0x39)}),(0x0,z[aL(0x1c)])('li',{'children':'运维工程师：增强网络故障排查能力'}),(0x0,z['jsx'])('li',{'children':'技术学生：学习和实践网络安全知识'})]}),(0x0,z['jsx'])('h3',{'children':'技术要求'}),(0x0,z['jsxs'])('ul',{'children':[(0x0,z['jsx'])('li',{'children':'熟悉HTTP/HTTPS协议'}),(0x0,z['jsx'])('li',{'children':'了解网络抓包工具使用'}),(0x0,z['jsx'])('li',{'children':'掌握基本的加密解密概念'}),(0x0,z['jsx'])('li',{'children':'具备JSON数据格式处理能力'}),(0x0,z['jsx'])('li',{'children':aL(0x3a)})]}),(0x0,z['jsx'])('h3',{'children':aL(0x3b)}),(0x0,z['jsxs'])('ol',{'children':[(0x0,z[aL(0x1c)])('li',{'children':'点击开始考试按钮'}),(0x0,z['jsx'])('li',{'children':'系统生成加密的网络请求'}),(0x0,z['jsx'])('li',{'children':'使用工具捕获和分析网络数据'}),(0x0,z['jsx'])('li',{'children':'解密请求和响应数据'}),(0x0,z['jsx'])('li',{'children':'提交解密结果进行验证'}),(0x0,z[aL(0x1c)])('li',{'children':'获得考试结果和技能评估'})]}),(0x0,z['jsx'])('h3',{'children':'相关关键词'}),(0x0,z['jsx'])('p',{'children':'网络调试,\x20数据解密,\x20抓包分析,\x20Wireshark,\x20F12调试,\x20网络安全,\x20技术测试,\x20程序员考试,\x20加密解密,\x20协议分析,\x20LinuxDo,\x20社区高考,\x20技能认证,\x20网络工程师,\x20安全研究,\x20运维技能,\x20JSON解析,\x20HTTP协议,\x20HTTPS加密'})]})]});}function S(W){const aM=b;let {language:X}=W,Y=G(X);return(0x0,z['jsx'])('section',{'className':'mb-8','children':(0x0,z['jsxs'])('div',{'className':'bg-white\x20bg-opacity-50\x20p-6\x20rounded-lg\x20shadow-sm\x20border\x20border-[#3d3929]\x20border-opacity-10','children':[(0x0,z['jsxs'])('h3',{'className':'text-xl\x20font-bold\x20text-[#3d3929]\x20mb-6\x20text-center','children':[aM(0x3c),Y['examInstructions']]}),(0x0,z['jsxs'])('div',{'className':aM(0x3d),'children':[(0x0,z['jsxs'])(aM(0x1e),{'className':'bg-white\x20bg-opacity-30\x20p-4\x20rounded-lg','children':[(0x0,z['jsxs'])('h4',{'className':'font-semibold\x20text-[#3d3929]\x20mb-3\x20flex\x20items-center','children':['📝\x20',Y['examSubject']]}),(0x0,z['jsxs'])('ul',{'className':'space-y-2\x20text-[#3d3929]\x20opacity-80','children':[(0x0,z['jsx'])('li',{'children':Y['subjectItem1']}),(0x0,z['jsx'])('li',{'children':Y['subjectItem2']}),(0x0,z['jsx'])('li',{'children':Y[aM(0x3e)]})]})]}),(0x0,z['jsxs'])('div',{'className':aM(0x3f),'children':[(0x0,z['jsxs'])('h4',{'className':aM(0x40),'children':['⏰\x20',Y['examTime']]}),(0x0,z['jsxs'])('ul',{'className':'space-y-2\x20text-[#3d3929]\x20opacity-80','children':[(0x0,z['jsx'])('li',{'children':Y['timeItem1']}),(0x0,z['jsx'])('li',{'children':Y['timeItem2']}),(0x0,z[aM(0x1c)])('li',{'children':Y['timeItem3']})]})]}),(0x0,z['jsxs'])('div',{'className':'bg-white\x20bg-opacity-30\x20p-4\x20rounded-lg','children':[(0x0,z['jsxs'])('h4',{'className':'font-semibold\x20text-[#3d3929]\x20mb-3\x20flex\x20items-center','children':['🛠️\x20',Y['requiredTools']]}),(0x0,z['jsxs'])('ul',{'className':'space-y-2\x20text-[#3d3929]\x20opacity-80','children':[(0x0,z['jsx'])('li',{'children':Y['toolItem1']}),(0x0,z['jsx'])('li',{'children':Y['toolItem2']}),(0x0,z['jsx'])('li',{'children':Y[aM(0x41)]})]})]}),(0x0,z[aM(0x19)])('div',{'className':'bg-white\x20bg-opacity-30\x20p-4\x20rounded-lg','children':[(0x0,z['jsxs'])('h4',{'className':'font-semibold\x20text-[#3d3929]\x20mb-3\x20flex\x20items-center','children':['🎯\x20',Y['gradingCriteria']]}),(0x0,z['jsxs'])('ul',{'className':'space-y-2\x20text-[#3d3929]\x20opacity-80','children':[(0x0,z['jsx'])('li',{'children':Y['gradeItem1']}),(0x0,z['jsx'])('li',{'children':Y['gradeItem2']}),(0x0,z['jsx'])('li',{'children':Y['gradeItem3']})]})]})]}),(0x0,z['jsxs'])('div',{'className':'mt-6\x20p-4\x20bg-[#da7756]\x20bg-opacity-10\x20border\x20border-[#da7756]\x20border-opacity-30\x20rounded-lg','children':[(0x0,z[aM(0x19)])('h4',{'className':'font-semibold\x20text-[#da7756]\x20mb-3\x20flex\x20items-center','children':['⚠️\x20',Y['importantReminder']]}),(0x0,z['jsxs'])('ul',{'className':aM(0x42),'children':[(0x0,z['jsx'])('li',{'children':Y['reminderItem1']}),(0x0,z['jsx'])('li',{'children':Y['reminderItem2']}),(0x0,z['jsx'])('li',{'children':Y['reminderItem3']}),(0x0,z[aM(0x1c)])('li',{'children':Y['reminderItem4']}),(0x0,z['jsx'])('li',{'children':Y['reminderItem5']})]})]}),(0x0,z['jsxs'])('div',{'className':'mt-6\x20p-4\x20bg-[#3d3929]\x20bg-opacity-5\x20border\x20border-[#3d3929]\x20border-opacity-20\x20rounded-lg','children':[(0x0,z[aM(0x19)])('h4',{'className':'font-semibold\x20text-[#3d3929]\x20mb-3\x20flex\x20items-center','children':['🏆\x20',Y[aM(0x43)]]}),(0x0,z[aM(0x1c)])('p',{'className':aM(0x44),'children':Y['passingDescription']})]})]})});}var T=w(0xeea);function U(W){const aN=b;let {language:X}=W,Y=G(X),[Z,a0]=(0x0,A['useState'])({'hours':0x0,'minutes':0x0,'seconds':0x0});(0x0,A['useEffect'])(()=>{let a2=()=>{try{let a4=(0x0,T['mu'])()['keyRotationInfo']['nextKeyRotationIn'];if(a4>0x0){let a5=Math['floor'](a4/0x3e8);a0({'hours':Math['floor'](a5/0xe10),'minutes':Math['floor'](a5%0xe10/0x3c),'seconds':a5%0x3c});}else a0({'hours':0x0,'minutes':0x0,'seconds':0x0});}catch(a6){console['error']('Failed\x20to\x20get\x20UTC\x20sync\x20info:',a6),a0({'hours':0x0,'minutes':0x0,'seconds':0x0});}};a2();let a3=setInterval(a2,0x3e8);return()=>clearInterval(a3);},[]);let a1=a2=>a2['toString']()['padStart'](0x2,'0');return(0x0,z['jsx'])('section',{'className':'mb-8','children':(0x0,z['jsx'])('div',{'className':'bg-white\x20bg-opacity-50\x20p-6\x20rounded-lg\x20shadow-sm\x20border\x20border-[#3d3929]\x20border-opacity-10','children':(0x0,z[aN(0x19)])('div',{'className':'text-center','children':[(0x0,z[aN(0x19)])('h3',{'className':'text-xl\x20font-bold\x20text-[#3d3929]\x20mb-6','children':['🔐\x20',Y['keyRefreshCountdown']]}),(0x0,z['jsxs'])('div',{'className':'bg-[#da7756]\x20bg-opacity-10\x20border\x20border-[#da7756]\x20border-opacity-30\x20rounded-lg\x20p-4\x20mb-4','children':[(0x0,z[aN(0x1c)])('p',{'className':'text-sm\x20font-medium\x20text-[#da7756]\x20mb-3','children':Y['keyRefreshIn']}),(0x0,z['jsxs'])('div',{'className':'flex\x20justify-center\x20items-center\x20space-x-2\x20text-lg\x20font-mono\x20font-bold\x20text-[#3d3929]','children':[(0x0,z[aN(0x1c)])('div',{'className':'bg-white\x20bg-opacity-50\x20rounded-lg\x20px-3\x20py-2\x20min-w-[50px]','children':(0x0,z[aN(0x19)])(aN(0x1e),{'className':'text-center','children':[(0x0,z[aN(0x1c)])('div',{'children':a1(Z['hours'])}),(0x0,z['jsx'])('div',{'className':'text-xs\x20text-[#3d3929]\x20opacity-60\x20mt-1\x20font-normal','children':Y['hours']})]})}),(0x0,z['jsx'])(aN(0x1e),{'className':'text-[#3d3929]\x20opacity-50\x20px-1','children':':'}),(0x0,z['jsx'])('div',{'className':'bg-white\x20bg-opacity-50\x20rounded-lg\x20px-3\x20py-2\x20min-w-[50px]','children':(0x0,z['jsxs'])('div',{'className':'text-center','children':[(0x0,z['jsx'])('div',{'children':a1(Z['minutes'])}),(0x0,z['jsx'])('div',{'className':aN(0x45),'children':Y['minutes']})]})}),(0x0,z[aN(0x1c)])('div',{'className':'text-[#3d3929]\x20opacity-50\x20px-1','children':':'}),(0x0,z['jsx'])('div',{'className':'bg-white\x20bg-opacity-50\x20rounded-lg\x20px-3\x20py-2\x20min-w-[50px]','children':(0x0,z['jsxs'])('div',{'className':'text-center','children':[(0x0,z['jsx'])('div',{'children':a1(Z['seconds'])}),(0x0,z[aN(0x1c)])('div',{'className':'text-xs\x20text-[#3d3929]\x20opacity-60\x20mt-1\x20font-normal','children':Y['seconds']})]})})]})]}),(0x0,z['jsx'])('p',{'className':'text-sm\x20text-[#3d3929]\x20opacity-80\x20leading-relaxed','children':Y['keyRefreshDescription']})]})})});}function V(){const aO=b;let [W,X]=(0x0,A['useState'])(!0x1),[Y,Z]=(0x0,A['useState'])(!0x1),[a0,a1]=(0x0,A[aO(0x46)])(''),[a2,a3]=(0x0,A['useState'])(''),[a4,a5]=(0x0,A['useState'])(null),[a6,a7]=(0x0,A['useState'])(null),[a8,a9]=(0x0,A['useState'])('zh'),aa=G(a8),ab=async()=>{X(!0x0),a5(null);try{let ad={'action':'tech_test','timestamp':Date['now'](),'challenge':'decode_this_message','user_id':'test_user_'+Math['random']()['toString'](0x24)['substring'](0x2,0xb)},ae=function(ag){const aR=b;try{let ah=JSON['stringify'](ag),ai=B['ZP']['gzip'](ah),aj=(function(){const aP=b;let ao=Date['now'](),ap=Math['random']()['toString'](0x24)[aP(0x6)](0x2),aq=''['concat'](C[aP(0x47)])['concat'](ao,'_')['concat'](ap,'_FAKE_DATA_END');return new TextEncoder()['encode'](aq);}()),ak=E(ai,aj),al=function(ao,ap){const aQ=b;let aq=new Uint8Array(ao[aQ(0x2)]),ar=new TextEncoder()['encode'](ap);for(let as=0x0;as<ao['length'];as++)aq[as]=ao[as]^ar[as%ar['length']];return aq;}(ak,C['staticKey']),am=new TextEncoder()['encode'](C['headerSignature']),an=E(am,al);return btoa(Array['from'](an,ao=>String['fromCharCode'](ao))[aR(0x48)](''));}catch(ao){throw console['error']('Data\x20compression\x20error:',ao),Error('Failed\x20to\x20compress\x20data');}}(ad),af=await fetch('/api/exam',{'method':'POST','headers':{'Content-Type':'application/json'},'body':JSON['stringify']({'data':ae})});if(!af['ok'])throw Error('Network\x20request\x20failed');await af['json'](),Z(!0x0);}catch(ag){console['error']('Test\x20request\x20failed:',ag),alert(aa['networkError']);}finally{X(!0x1);}},ac=async()=>{const aS=b;if(!a0['trim']()||!a2['trim']()){alert(aa['fillAllFields']);return;}X(!0x0);try{let ad=await fetch('/api/exam/verify',{'method':'POST','headers':{'Content-Type':'application/json'},'body':JSON[aS(0x49)]({'requestData':a0['trim'](),'responseData':a2['trim']()})});if(!ad['ok'])throw Error('Verification\x20request\x20failed');let ae=await ad['json']();a7(ae),a5(ae['success']?aa['verificationSuccess']:aa['verificationFailed']);}catch(af){console['error']('Verification\x20failed:',af),a5(aa['verificationError']);}finally{X(!0x1);}};return(0x0,z['jsxs'])('div',{'className':'bg-[#eeece2]\x20text-[#3d3929]\x20min-h-screen\x20flex\x20flex-col','children':[(0x0,z['jsx'])(R,{'language':a8}),(0x0,z['jsx'])(Q,{'currentLanguage':a8,'onLanguageChange':ad=>{a9(ad);}}),(0x0,z['jsx'])(H,{'language':a8}),(0x0,z['jsx'])(aO(0x4a),{'className':'flex-grow\x20flex\x20flex-col\x20px-4\x20sm:px-8\x20md:px-16\x20py-8\x20sm:py-12','children':(0x0,z[aO(0x19)])('div',{'className':'max-w-4xl\x20mx-auto\x20w-full','children':[(0x0,z['jsx'])(I,{'language':a8}),(0x0,z['jsx'])(S,{'language':a8}),(0x0,z[aO(0x1c)])(U,{'language':a8}),!Y&&(0x0,z['jsx'])(J,{'isLoading':W,'onStartTest':ab,'language':a8}),Y&&(0x0,z['jsx'])(K,{'requestInput':a0,'responseInput':a2,'isLoading':W,'onRequestInputChange':a1,'onResponseInputChange':a3,'onSubmitVerification':ac,'onReset':()=>{Z(!0x1),a1(''),a3(''),a5(null),a7(null);},'language':a8}),a4&&(0x0,z['jsx'])(M,{'result':a4,'verificationData':a6,'language':a8}),(0x0,z['jsx'])(O,{'language':a8})]})}),(0x0,z['jsx'])(P,{'language':a8})]});}}},function(c){c['O'](0x0,[0x7d,0x3cb,0xd8,0x2e8],function(){return c(c['s']=0x23b0);}),_N_E=c['O']();}]);