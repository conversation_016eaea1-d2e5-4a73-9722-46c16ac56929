#!/usr/bin/env python3
"""
基于JavaScript代码分析的最终解决方案
"""

import gzip
import json
import base64
import requests
import time
import random

class LinuxDoFinalSolver:
    def __init__(self):
        self.base_url = "https://oldjs-gaokaolinuxdo.deno.dev"
        self.api_endpoint = f"{self.base_url}/api/exam"
        self.session = requests.Session()
        
        # 从JavaScript代码中提取的关键信息
        self.static_key = "TechExam2024_EncryptionKey_ForTesting"
        self.header_signature = "----TECH_EXAM_2024----"
        self.obfuscation_marker = "___OBFUSCATION_DATA___"

    def xor_encrypt_decrypt(self, data: bytes, key: str) -> bytes:
        """XOR加密/解密函数"""
        key_bytes = key.encode('utf-8')
        key_len = len(key_bytes)
        result = bytearray()
        
        for i, byte in enumerate(data):
            result.append(byte ^ key_bytes[i % key_len])
        
        return bytes(result)

    def concat_bytes(self, *arrays):
        """连接字节数组"""
        total_length = sum(len(arr) for arr in arrays)
        result = bytearray(total_length)
        offset = 0
        
        for arr in arrays:
            result[offset:offset+len(arr)] = arr
            offset += len(arr)
        
        return bytes(result)

    def create_obfuscation_data(self):
        """创建混淆数据（基于JavaScript逻辑）"""
        timestamp = int(time.time() * 1000)  # JavaScript使用毫秒
        random_str = hex(int(random.random() * 0xffffff))[2:]

        obfuscation_content = f"{self.obfuscation_marker}{timestamp}_{random_str}_FAKE_DATA_END"
        return obfuscation_content.encode('utf-8')

    def encrypt_data(self, data: dict) -> str:
        """根据JavaScript逻辑加密数据"""
        try:
            # 1. JSON序列化
            json_str = json.dumps(data, ensure_ascii=False)
            json_bytes = json_str.encode('utf-8')
            
            # 2. gzip压缩
            compressed = gzip.compress(json_bytes)
            
            # 3. 创建混淆数据
            obfuscation_data = self.create_obfuscation_data()
            
            # 4. 连接压缩数据和混淆数据
            combined = self.concat_bytes(compressed, obfuscation_data)
            
            # 5. XOR加密
            encrypted = self.xor_encrypt_decrypt(combined, self.static_key)
            
            # 6. 添加头部签名
            header_bytes = self.header_signature.encode('utf-8')
            final_data = self.concat_bytes(header_bytes, encrypted)
            
            # 7. Base64编码
            return base64.b64encode(final_data).decode('ascii')
            
        except Exception as e:
            print(f"加密失败: {e}")
            return ""

    def decrypt_data(self, encrypted_data: str) -> dict:
        """解密数据"""
        try:
            # 1. Base64解码
            decoded = base64.b64decode(encrypted_data)
            
            # 2. 移除头部签名
            header_bytes = self.header_signature.encode('utf-8')
            if not decoded.startswith(header_bytes):
                raise ValueError("Invalid header signature")
            
            encrypted_payload = decoded[len(header_bytes):]
            
            # 3. XOR解密
            decrypted = self.xor_encrypt_decrypt(encrypted_payload, self.static_key)
            
            # 4. 尝试分离gzip数据和混淆数据
            # 尝试不同的分割点
            for split_point in range(10, len(decrypted) - 50):
                try:
                    gzip_data = decrypted[:split_point]
                    decompressed = gzip.decompress(gzip_data)
                    json_str = decompressed.decode('utf-8')
                    return json.loads(json_str)
                except:
                    continue
            
            # 如果上面失败，尝试从后往前
            for split_point in range(len(decrypted) - 100, 50, -1):
                try:
                    gzip_data = decrypted[:split_point]
                    decompressed = gzip.decompress(gzip_data)
                    json_str = decompressed.decode('utf-8')
                    return json.loads(json_str)
                except:
                    continue
            
            raise ValueError("Failed to extract valid JSON data")
            
        except Exception as e:
            print(f"解密失败: {e}")
            return None

    def send_test_request(self):
        """发送测试请求"""
        print("发送测试请求...")
        
        # 准备请求数据（基于JavaScript代码）
        request_data = {
            "action": "tech_test",
            "timestamp": int(time.time() * 1000),
            "challenge": "decode_this_message",
            "user_id": f"test_user_{hex(int(random.random() * 0xffffff))[2:9]}"
        }
        
        print(f"请求数据: {json.dumps(request_data, indent=2, ensure_ascii=False)}")
        
        # 加密数据
        encrypted_payload = self.encrypt_data(request_data)
        if not encrypted_payload:
            print("加密失败")
            return None
        
        print(f"加密后的数据长度: {len(encrypted_payload)}")
        
        # 发送请求
        try:
            response = self.session.post(
                self.api_endpoint,
                json={"data": encrypted_payload},
                headers={
                    "Content-Type": "application/json",
                    "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36"
                },
                timeout=30
            )
            
            print(f"响应状态: {response.status_code}")
            print(f"响应头: {dict(response.headers)}")
            
            if response.status_code == 200:
                print(f"响应内容: {response.text}")
                return response.text
            else:
                print(f"请求失败: {response.text}")
                return None
                
        except Exception as e:
            print(f"发送请求失败: {e}")
            return None

    def solve_challenge(self):
        """解决挑战"""
        print("开始解决LinuxDo社区技术高考挑战...")
        print(f"使用的密钥: {self.static_key}")
        print(f"使用的头部签名: {self.header_signature}")
        
        # 发送测试请求
        response = self.send_test_request()
        
        if response:
            print("=" * 50)
            print("挑战解决成功!")
            print("响应数据:")
            print(response)
            print("=" * 50)
            
            # 尝试解密响应（如果是加密的）
            try:
                decrypted_response = self.decrypt_data(response)
                if decrypted_response:
                    print("解密后的响应:")
                    print(json.dumps(decrypted_response, indent=2, ensure_ascii=False))
            except:
                print("响应可能不是加密格式")
            
            return response
        else:
            print("挑战解决失败")
            return None

if __name__ == "__main__":
    solver = LinuxDoFinalSolver()
    result = solver.solve_challenge()
    
    if result:
        print("\n任务完成!")
    else:
        print("\n需要进一步调试")
