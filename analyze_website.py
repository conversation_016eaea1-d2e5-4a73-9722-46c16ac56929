#!/usr/bin/env python3
"""
分析LinuxDo网站的详细信息
"""

import requests
import re
import json
from urllib.parse import urljoin

class WebsiteAnalyzer:
    def __init__(self):
        self.base_url = "https://oldjs-gaokaolinuxdo.deno.dev"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        })

    def get_main_page(self):
        """获取主页内容"""
        print("获取主页内容...")
        response = self.session.get(self.base_url)
        print(f"状态码: {response.status_code}")
        return response.text

    def extract_js_files(self, html_content):
        """提取JavaScript文件链接"""
        print("提取JavaScript文件...")
        js_files = []
        
        # 查找script标签
        script_pattern = r'<script[^>]*src=["\']([^"\']+)["\'][^>]*>'
        matches = re.findall(script_pattern, html_content)
        
        for match in matches:
            if match.startswith('/_next/'):
                full_url = urljoin(self.base_url, match)
                js_files.append(full_url)
                print(f"找到JS文件: {match}")
        
        return js_files

    def analyze_js_file(self, js_url):
        """分析JavaScript文件内容"""
        print(f"分析JS文件: {js_url}")
        try:
            response = self.session.get(js_url)
            if response.status_code == 200:
                content = response.text
                
                # 查找可能的加密相关代码
                keywords = [
                    'encrypt', 'decrypt', 'gzip', 'xor', 'compress',
                    'exam', 'challenge', 'api', 'post', 'fetch',
                    'LinuxDo', 'gaokao', 'header'
                ]
                
                found_keywords = []
                for keyword in keywords:
                    if keyword.lower() in content.lower():
                        found_keywords.append(keyword)
                
                if found_keywords:
                    print(f"  找到关键词: {found_keywords}")
                    
                    # 查找函数定义
                    function_patterns = [
                        r'function\s+(\w*encrypt\w*)\s*\(',
                        r'function\s+(\w*compress\w*)\s*\(',
                        r'function\s+(\w*exam\w*)\s*\(',
                        r'(\w*encrypt\w*)\s*[:=]\s*function',
                        r'(\w*compress\w*)\s*[:=]\s*function'
                    ]
                    
                    for pattern in function_patterns:
                        matches = re.findall(pattern, content, re.IGNORECASE)
                        if matches:
                            print(f"  找到函数: {matches}")
                
                return content
            else:
                print(f"  获取失败: {response.status_code}")
                return None
        except Exception as e:
            print(f"  分析失败: {e}")
            return None

    def find_api_endpoints(self, html_content):
        """查找API端点"""
        print("查找API端点...")
        
        # 查找可能的API路径
        api_patterns = [
            r'["\'](/api/[^"\']+)["\']',
            r'["\']([^"\']*api[^"\']*)["\']',
            r'fetch\s*\(\s*["\']([^"\']+)["\']',
            r'post\s*\(\s*["\']([^"\']+)["\']'
        ]
        
        endpoints = set()
        for pattern in api_patterns:
            matches = re.findall(pattern, html_content, re.IGNORECASE)
            for match in matches:
                if 'api' in match.lower():
                    endpoints.add(match)
        
        for endpoint in endpoints:
            print(f"找到端点: {endpoint}")
            
        return list(endpoints)

    def test_api_endpoints(self, endpoints):
        """测试API端点"""
        print("测试API端点...")
        
        for endpoint in endpoints:
            full_url = urljoin(self.base_url, endpoint)
            print(f"测试: {full_url}")
            
            # 测试GET请求
            try:
                response = self.session.get(full_url)
                print(f"  GET {response.status_code}: {response.text[:200]}")
            except Exception as e:
                print(f"  GET失败: {e}")
            
            # 测试POST请求
            try:
                response = self.session.post(full_url, json={})
                print(f"  POST {response.status_code}: {response.text[:200]}")
            except Exception as e:
                print(f"  POST失败: {e}")

    def search_for_clues(self, content):
        """搜索线索"""
        print("搜索加密线索...")
        
        # 查找可能的密钥
        key_patterns = [
            r'key\s*[:=]\s*["\']([^"\']+)["\']',
            r'password\s*[:=]\s*["\']([^"\']+)["\']',
            r'secret\s*[:=]\s*["\']([^"\']+)["\']',
            r'["\']([A-Za-z0-9]{8,})["\']'  # 可能的密钥字符串
        ]
        
        potential_keys = set()
        for pattern in key_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            for match in matches:
                if len(match) >= 4 and len(match) <= 20:
                    potential_keys.add(match)
        
        print(f"可能的密钥: {list(potential_keys)[:10]}")  # 只显示前10个
        
        return list(potential_keys)

    def run_analysis(self):
        """运行完整分析"""
        print("开始网站分析...")
        
        # 获取主页
        html_content = self.get_main_page()
        
        # 提取JS文件
        js_files = self.extract_js_files(html_content)
        
        # 查找API端点
        endpoints = self.find_api_endpoints(html_content)
        
        # 测试API端点
        if endpoints:
            self.test_api_endpoints(endpoints)
        
        # 分析JS文件
        all_js_content = ""
        for js_file in js_files[:5]:  # 只分析前5个文件
            content = self.analyze_js_file(js_file)
            if content:
                all_js_content += content + "\n"
        
        # 搜索线索
        if all_js_content:
            potential_keys = self.search_for_clues(all_js_content)
            return potential_keys
        
        return []

if __name__ == "__main__":
    analyzer = WebsiteAnalyzer()
    keys = analyzer.run_analysis()
    
    if keys:
        print(f"\n发现的潜在密钥: {keys[:20]}")  # 显示前20个
    else:
        print("\n未发现明显的密钥线索")
