#!/usr/bin/env python3
"""
简单的解密脚本
"""

import gzip
import json
import base64

def xor_decrypt(data: bytes, key: str) -> bytes:
    """XOR解密"""
    key_bytes = key.encode('utf-8')
    key_len = len(key_bytes)
    result = bytearray()
    
    for i, byte in enumerate(data):
        result.append(byte ^ key_bytes[i % key_len])
    
    return bytes(result)

def decrypt_response():
    """解密响应数据"""
    # 响应数据
    encrypted_data = "LS0tLVRFQ0hfRVhBTV8yMDI0LS0tLUvua2hFeGFtMjNfZZQLrVNihWW/Pr0pmcHWdJr+Chxt91a+sEN/d1is3tLEh42DRD8DfYrSrIi8mIUmFBlRA+bTEaZO2eT9hZbEPoXQswPNTLUotNBSECXWLpLQNLcEV8blqZ8GGH2a8rFMHG/PkmDp+bicOB9qSAjEECnLdFKynrhoKzattNseV07MOl0XMc/HAeYUNaBnhuCtDWeRLG9ld/tRCRwFAGtZ3Pkj8Ok0HQk2iqfMicOZlju/4NKDZhMFkD44+oK0kcY6QKzDTGIniDRZA9mTzeO8qcd02gIRypZRVuReWlvLqpdDQ6byOQ8wwi6B0tWMDLTNsEIhlnLoD/9aSSYqJPh1LtYJo+NmxhmhXP29NX/8lzn3t8CgqkSZ0ypORPjsAzPVGOWqcpk+Sy5DQlc9JhUZDufyxnAsBFzse2MqSWArp9KOVMWyq8tRF90OkESnW9UA2DvfrolG+MyJy4cvvDgMy2JfpF/XoGInmxgT9lK3il3J4eaLAmxjciYvKyYtKB42Oh4SJj0aOjc1PS84CzpSX3FBVF4AAwUNaHNePEYAQhUGAB0uBEo2GSkzHyAsMCg6JgsgLSw="
    
    static_key = "TechExam2024_EncryptionKey_ForTesting"
    header_signature = "----TECH_EXAM_2024----"
    
    print("开始解密...")
    
    # 1. Base64解码
    decoded = base64.b64decode(encrypted_data)
    print(f"Base64解码后长度: {len(decoded)}")
    
    # 2. 移除头部
    header_bytes = header_signature.encode('utf-8')
    if decoded.startswith(header_bytes):
        payload = decoded[len(header_bytes):]
        print(f"移除头部后长度: {len(payload)}")
    else:
        print("头部不匹配")
        return None
    
    # 3. XOR解密
    decrypted = xor_decrypt(payload, static_key)
    print(f"XOR解密后长度: {len(decrypted)}")
    
    # 4. 检查是否以gzip魔数开始
    if decrypted.startswith(b'\x1f\x8b'):
        print("发现gzip魔数，尝试解压...")
        
        # 尝试不同的长度
        for length in range(len(decrypted), 50, -10):
            try:
                gzip_data = decrypted[:length]
                decompressed = gzip.decompress(gzip_data)
                json_str = decompressed.decode('utf-8')
                result = json.loads(json_str)
                
                print(f"成功解密! 使用长度: {length}")
                print("解密结果:")
                print(json.dumps(result, indent=2, ensure_ascii=False))
                return result
                
            except Exception as e:
                if length % 100 == 0:
                    print(f"长度 {length} 失败: {str(e)[:50]}")
                continue
    
    print("解密失败")
    return None

if __name__ == "__main__":
    result = decrypt_response()
    
    if result:
        print("\n=" * 50)
        print("解密成功!")
        print("最终结果:")
        print(json.dumps(result, indent=2, ensure_ascii=False))
        print("=" * 50)
    else:
        print("解密失败")
