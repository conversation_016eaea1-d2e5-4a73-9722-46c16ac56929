// ==UserScript==
// @name         通用论坛文章复制插件
// @namespace    http://tampermonkey.net/
// @version      2.0.0
// @description  为各种论坛提供文章复制和导出功能，支持Discourse、phpBB、XenForo、vBulletin等，导出Markdown、HTM<PERSON>、PDF、PNG格式
// <AUTHOR> Assistant
// @match        https://*/*
// @match        http://*/*
// @grant        GM_addStyle
// @grant        GM_download
// @grant        GM_setClipboard
// @require      https://cdn.tailwindcss.com/3.3.0
// @require      https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js
// @require      https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js
// @require      https://cdnjs.cloudflare.com/ajax/libs/turndown/7.1.2/turndown.min.js
// @run-at       document-end
// ==/UserScript==

(function () {
  'use strict';

  // 全局变量
  let isInitialized = false;
  let currentPost = null;
  let turndownService = null;

  // 初始化TailwindCSS
  function initTailwind() {
    if (typeof tailwind !== 'undefined') {
      tailwind.config = {
        corePlugins: {
          preflight: false,
        }
      };
    }
  }

  // 初始化Turndown服务
  function initTurndown() {
    if (typeof TurndownService !== 'undefined') {
      turndownService = new TurndownService({
        headingStyle: 'atx',
        codeBlockStyle: 'fenced',
        fence: '```',
        emDelimiter: '*',
        strongDelimiter: '**',
        linkStyle: 'inlined',
        linkReferenceStyle: 'full'
      });

      // 自定义规则处理Discourse特有元素
      turndownService.addRule('discourseQuote', {
        filter: '.quote',
        replacement: function (content, node) {
          return '\n> ' + content.replace(/\n/g, '\n> ') + '\n\n';
        }
      });

      turndownService.addRule('discourseCodeBlock', {
        filter: function (node) {
          return node.nodeName === 'PRE' && node.querySelector('code');
        },
        replacement: function (content, node) {
          const codeElement = node.querySelector('code');
          const language = codeElement.className.match(/language-(\w+)/);
          const lang = language ? language[1] : '';
          return '\n```' + lang + '\n' + codeElement.textContent + '\n```\n\n';
        }
      });
    }
  }

  // 检测论坛类型
  function detectForumType() {
    // Discourse
    if (document.querySelector('meta[name="generator"]')?.content?.includes('Discourse') ||
      document.querySelector('.discourse-root') ||
      document.querySelector('#discourse-modal') ||
      window.Discourse ||
      document.body.classList.contains('discourse')) {
      return 'discourse';
    }

    // phpBB
    if (document.querySelector('meta[name="generator"]')?.content?.includes('phpBB') ||
      document.querySelector('.phpbb') ||
      document.querySelector('#phpbb') ||
      document.querySelector('.forumbg') ||
      document.querySelector('.postbody')) {
      return 'phpbb';
    }

    // XenForo
    if (document.querySelector('meta[name="generator"]')?.content?.includes('XenForo') ||
      document.querySelector('.XenForo') ||
      document.querySelector('.message-content') ||
      document.querySelector('.bbWrapper') ||
      document.querySelector('.p-body-main')) {
      return 'xenforo';
    }

    // vBulletin
    if (document.querySelector('meta[name="generator"]')?.content?.includes('vBulletin') ||
      document.querySelector('.vbulletin') ||
      document.querySelector('.postbit') ||
      document.querySelector('.post_message')) {
      return 'vbulletin';
    }

    // NodeBB
    if (document.querySelector('meta[name="generator"]')?.content?.includes('NodeBB') ||
      document.querySelector('.nodebb') ||
      document.querySelector('[component="post"]') ||
      window.app?.template) {
      return 'nodebb';
    }

    // Flarum
    if (document.querySelector('meta[name="generator"]')?.content?.includes('Flarum') ||
      document.querySelector('.flarum') ||
      document.querySelector('.Post-body') ||
      window.app?.forum) {
      return 'flarum';
    }

    // 通用论坛检测
    if (document.querySelector('.post, .message, .topic, .thread, .forum-post, .comment') ||
      document.querySelector('[class*="post"], [class*="message"], [class*="topic"]') ||
      document.querySelector('#post, #message, #topic, #thread')) {
      return 'generic';
    }

    return null;
  }

  // 检测是否为论坛页面
  function isForum() {
    return detectForumType() !== null;
  }

  // 根据论坛类型获取选择器配置
  function getForumSelectors(forumType) {
    const selectors = {
      discourse: {
        title: 'h1.fancy-title, .topic-title h1, h1[data-topic-title]',
        author: '.username, .creator .username, [data-user-card]',
        time: '.post-date, .relative-date, time',
        content: '.cooked, .post-content, .topic-body',
        post: '.topic-post:first-child, .post-stream .topic-post:first-child, article.boxed:first-child',
        removeElements: '.post-menu, .topic-map, .post-links, .post-actions, .like-button, .reply-button, .share-button, .flag-button, .post-admin-menu'
      },
      phpbb: {
        title: 'h1, .topic-title, .forum-title',
        author: '.username, .author, .post-author',
        time: '.post-date, .postdate, time',
        content: '.postbody, .content, .post-content',
        post: '.post:first-child, .postbody:first-child, .topic:first-child',
        removeElements: '.post-buttons, .signature, .post-edit-reason'
      },
      xenforo: {
        title: 'h1, .p-title-value, .thread-title',
        author: '.username, .message-name, .p-navgroup-link--user',
        time: '.message-date, .u-dt, time',
        content: '.message-content, .bbWrapper, .message-body',
        post: '.message:first-child, .post:first-child',
        removeElements: '.message-actionBar, .message-signature, .reactionsBar'
      },
      vbulletin: {
        title: 'h1, .thread-title, .topic-title',
        author: '.username, .bigusername, .post-author',
        time: '.post-date, .postdate, .date',
        content: '.post_message, .content, .post-content',
        post: '.postbit:first-child, .post:first-child',
        removeElements: '.postbit-buttons, .signature, .post-thanks'
      },
      nodebb: {
        title: 'h1, [component="topic/title"]',
        author: '[component="post/username"], .username',
        time: '[component="post/relativeTime"], .timeago',
        content: '[component="post/content"], .content',
        post: '[component="post"]:first-child, .post:first-child',
        removeElements: '.post-tools, .post-signature'
      },
      flarum: {
        title: 'h1, .item-title',
        author: '.username, .PostUser-name',
        time: '.PostMeta-time, time',
        content: '.Post-body, .content',
        post: '.Post:first-child, .post:first-child',
        removeElements: '.Post-footer, .Post-actions'
      },
      generic: {
        title: 'h1, h2, .title, .topic-title, .thread-title, .subject',
        author: '.author, .username, .user, .poster, .by-author',
        time: '.date, .time, .timestamp, .posted, time',
        content: '.content, .message, .post-content, .body, .text',
        post: '.post:first-child, .message:first-child, .topic:first-child, .thread:first-child',
        removeElements: '.actions, .buttons, .signature, .footer, .meta'
      }
    };

    return selectors[forumType] || selectors.generic;
  }

  // 提取帖子内容
  function extractPostContent(postElement, forumType = null) {
    if (!postElement) return null;

    const currentForumType = forumType || detectForumType() || 'generic';
    const selectors = getForumSelectors(currentForumType);

    const titleElement = document.querySelector(selectors.title);
    const authorElement = postElement.querySelector(selectors.author);
    const timeElement = postElement.querySelector(selectors.time);
    const contentElement = postElement.querySelector(selectors.content);

    return {
      title: titleElement?.textContent?.trim() || document.title || '未知标题',
      author: authorElement?.textContent?.trim() || '未知作者',
      time: timeElement?.getAttribute('datetime') || timeElement?.textContent?.trim() || '未知时间',
      content: contentElement || postElement,
      url: window.location.href,
      forumType: currentForumType,
      selectors: selectors
    };
  }

  // 获取当前帖子
  function getCurrentPost() {
    // 尝试多种选择器匹配不同的Discourse版本
    const selectors = [
      '.topic-post:first-child',
      '.post-stream .topic-post:first-child',
      'article.boxed:first-child',
      '.post:first-child',
      '#post_1',
      '[data-post-id="1"]'
    ];

    for (const selector of selectors) {
      const element = document.querySelector(selector);
      if (element) {
        return extractPostContent(element);
      }
    }

    // 如果没找到特定帖子，尝试获取主要内容区域
    const mainContent = document.querySelector('.topic-area, .container.posts, main');
    if (mainContent) {
      return extractPostContent(mainContent);
    }

    return null;
  }

  // 转换为Markdown格式
  function convertToMarkdown(postData) {
    if (!turndownService || !postData) return '';

    let markdown = `# ${postData.title}\n\n`;
    markdown += `**作者：** ${postData.author}\n`;
    markdown += `**时间：** ${postData.time}\n`;
    markdown += `**链接：** ${postData.url}\n\n`;
    markdown += '---\n\n';

    if (postData.content) {
      // 克隆内容以避免修改原始DOM
      const clonedContent = postData.content.cloneNode(true);

      // 移除不需要的元素
      const elementsToRemove = clonedContent.querySelectorAll(
        '.post-menu, .topic-map, .post-links, .post-actions, ' +
        '.like-button, .reply-button, .share-button, .flag-button, ' +
        '.post-admin-menu, .post-date, .username'
      );
      elementsToRemove.forEach(el => el.remove());

      markdown += turndownService.turndown(clonedContent.innerHTML);
    }

    return markdown;
  }

  // 转换为HTML格式
  function convertToHTML(postData) {
    if (!postData) return '';

    let html = `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${postData.title}</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; max-width: 800px; margin: 0 auto; padding: 20px; }
        .header { border-bottom: 1px solid #eee; padding-bottom: 20px; margin-bottom: 20px; }
        .meta { color: #666; font-size: 14px; }
        .content { margin-top: 20px; }
        pre { background: #f5f5f5; padding: 15px; border-radius: 5px; overflow-x: auto; }
        blockquote { border-left: 4px solid #ddd; margin: 0; padding-left: 20px; color: #666; }
        img { max-width: 100%; height: auto; }
    </style>
</head>
<body>
    <div class="header">
        <h1>${postData.title}</h1>
        <div class="meta">
            <p><strong>作者：</strong> ${postData.author}</p>
            <p><strong>时间：</strong> ${postData.time}</p>
            <p><strong>链接：</strong> <a href="${postData.url}">${postData.url}</a></p>
        </div>
    </div>
    <div class="content">`;

    if (postData.content) {
      const clonedContent = postData.content.cloneNode(true);

      // 移除不需要的元素
      const elementsToRemove = clonedContent.querySelectorAll(
        '.post-menu, .topic-map, .post-links, .post-actions, ' +
        '.like-button, .reply-button, .share-button, .flag-button, ' +
        '.post-admin-menu, .post-date, .username'
      );
      elementsToRemove.forEach(el => el.remove());

      html += clonedContent.innerHTML;
    }

    html += `
    </div>
</body>
</html>`;

    return html;
  }

  // 复制到剪贴板
  function copyToClipboard(text, format) {
    if (typeof GM_setClipboard !== 'undefined') {
      GM_setClipboard(text);
      showNotification(`${format}格式已复制到剪贴板！`, 'success');
    } else {
      // 降级方案
      navigator.clipboard.writeText(text).then(() => {
        showNotification(`${format}格式已复制到剪贴板！`, 'success');
      }).catch(() => {
        showNotification('复制失败，请手动复制', 'error');
      });
    }
  }

  // 导出为PDF
  async function exportToPDF(postData) {
    if (typeof jsPDF === 'undefined') {
      showNotification('PDF库未加载，请刷新页面重试', 'error');
      return;
    }

    try {
      showNotification('正在生成PDF...', 'info');

      const { jsPDF } = window.jspdf;
      const doc = new jsPDF();

      // 设置中文字体（如果可用）
      doc.setFont('helvetica');
      doc.setFontSize(16);

      // 添加标题
      doc.text(postData.title, 20, 30);

      doc.setFontSize(12);
      doc.text(`作者: ${postData.author}`, 20, 50);
      doc.text(`时间: ${postData.time}`, 20, 65);
      doc.text(`链接: ${postData.url}`, 20, 80);

      // 添加内容（简化版本）
      if (postData.content) {
        const textContent = postData.content.textContent || '';
        const lines = doc.splitTextToSize(textContent, 170);
        doc.text(lines, 20, 100);
      }

      const fileName = `${postData.title.replace(/[^\w\s]/gi, '')}_${Date.now()}.pdf`;
      doc.save(fileName);

      showNotification('PDF导出成功！', 'success');
    } catch (error) {
      console.error('PDF导出失败:', error);
      showNotification('PDF导出失败', 'error');
    }
  }

  // 导出为PNG
  async function exportToPNG(postData) {
    if (typeof html2canvas === 'undefined') {
      showNotification('截图库未加载，请刷新页面重试', 'error');
      return;
    }

    try {
      showNotification('正在生成PNG...', 'info');

      const element = postData.content || document.querySelector('.topic-area, main');
      if (!element) {
        showNotification('未找到可截图的内容', 'error');
        return;
      }

      const canvas = await html2canvas(element, {
        backgroundColor: '#ffffff',
        scale: 2,
        logging: false,
        useCORS: true
      });

      const fileName = `${postData.title.replace(/[^\w\s]/gi, '')}_${Date.now()}.png`;

      if (typeof GM_download !== 'undefined') {
        const dataURL = canvas.toDataURL('image/png');
        GM_download(dataURL, fileName);
      } else {
        // 降级方案
        const link = document.createElement('a');
        link.download = fileName;
        link.href = canvas.toDataURL('image/png');
        link.click();
      }

      showNotification('PNG导出成功！', 'success');
    } catch (error) {
      console.error('PNG导出失败:', error);
      showNotification('PNG导出失败', 'error');
    }
  }

  // 显示通知
  function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 px-4 py-2 rounded-lg shadow-lg text-white transition-all duration-300 ${type === 'success' ? 'bg-green-500' :
      type === 'error' ? 'bg-red-500' :
        type === 'warning' ? 'bg-yellow-500' :
          'bg-blue-500'
      }`;
    notification.textContent = message;

    document.body.appendChild(notification);

    setTimeout(() => {
      notification.style.opacity = '0';
      setTimeout(() => {
        document.body.removeChild(notification);
      }, 300);
    }, 3000);
  }

  // 创建操作面板
  function createControlPanel() {
    const panel = document.createElement('div');
    panel.id = 'discourse-copy-panel';
    panel.className = 'fixed bottom-4 right-4 z-50 bg-white rounded-lg shadow-xl border border-gray-200 p-4 min-w-64';
    panel.style.display = 'none';

    panel.innerHTML = `
            <div class="flex items-center justify-between mb-3">
                <h3 class="text-lg font-semibold text-gray-800">文章导出</h3>
                <button id="close-panel" class="text-gray-400 hover:text-gray-600 text-xl">&times;</button>
            </div>
            <div class="space-y-2">
                <button id="copy-markdown" class="w-full px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors">
                    📝 复制Markdown
                </button>
                <button id="copy-html" class="w-full px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 transition-colors">
                    🌐 复制HTML
                </button>
                <button id="export-pdf" class="w-full px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors">
                    📄 导出PDF
                </button>
                <button id="export-png" class="w-full px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600 transition-colors">
                    🖼️ 导出PNG
                </button>
            </div>
        `;

    return panel;
  }

  // 创建浮动按钮
  function createFloatingButton() {
    const button = document.createElement('button');
    button.id = 'discourse-copy-button';
    button.className = 'fixed bottom-4 right-4 z-50 w-14 h-14 bg-blue-500 text-white rounded-full shadow-lg hover:bg-blue-600 transition-all duration-300 flex items-center justify-center text-xl';
    button.innerHTML = '📋';
    button.title = '文章复制工具';

    return button;
  }

  // 初始化插件
  function initPlugin() {
    if (isInitialized || !isDiscourse()) return;

    console.log('初始化Discourse文章复制插件...');

    // 初始化依赖
    initTailwind();
    initTurndown();

    // 创建UI元素
    const floatingButton = createFloatingButton();
    const controlPanel = createControlPanel();

    document.body.appendChild(floatingButton);
    document.body.appendChild(controlPanel);

    // 绑定事件
    floatingButton.addEventListener('click', () => {
      currentPost = getCurrentPost();
      if (!currentPost) {
        showNotification('未找到可复制的帖子内容', 'warning');
        return;
      }

      const panel = document.getElementById('discourse-copy-panel');
      panel.style.display = panel.style.display === 'none' ? 'block' : 'none';
    });

    document.getElementById('close-panel').addEventListener('click', () => {
      document.getElementById('discourse-copy-panel').style.display = 'none';
    });

    document.getElementById('copy-markdown').addEventListener('click', () => {
      if (currentPost) {
        const markdown = convertToMarkdown(currentPost);
        copyToClipboard(markdown, 'Markdown');
      }
    });

    document.getElementById('copy-html').addEventListener('click', () => {
      if (currentPost) {
        const html = convertToHTML(currentPost);
        copyToClipboard(html, 'HTML');
      }
    });

    document.getElementById('export-pdf').addEventListener('click', () => {
      if (currentPost) {
        exportToPDF(currentPost);
      }
    });

    document.getElementById('export-png').addEventListener('click', () => {
      if (currentPost) {
        exportToPNG(currentPost);
      }
    });

    // 点击外部关闭面板
    document.addEventListener('click', (e) => {
      const panel = document.getElementById('discourse-copy-panel');
      const button = document.getElementById('discourse-copy-button');

      if (!panel.contains(e.target) && !button.contains(e.target)) {
        panel.style.display = 'none';
      }
    });

    isInitialized = true;
    showNotification('Discourse文章复制插件已加载', 'success');
  }

  // 等待页面加载完成后初始化
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initPlugin);
  } else {
    // 延迟初始化以确保所有依赖都已加载
    setTimeout(initPlugin, 1000);
  }

  // 监听页面变化（SPA路由）
  let lastUrl = location.href;
  new MutationObserver(() => {
    const url = location.href;
    if (url !== lastUrl) {
      lastUrl = url;
      setTimeout(initPlugin, 500);
    }
  }).observe(document, { subtree: true, childList: true });

})();
