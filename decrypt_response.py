#!/usr/bin/env python3
"""
解密响应数据
"""

import gzip
import json
import base64

class ResponseDecryptor:
    def __init__(self):
        self.static_key = "TechExam2024_EncryptionKey_ForTesting"
        self.header_signature = "----TECH_EXAM_2024----"

    def xor_encrypt_decrypt(self, data: bytes, key: str) -> bytes:
        """XOR加密/解密函数"""
        key_bytes = key.encode('utf-8')
        key_len = len(key_bytes)
        result = bytearray()
        
        for i, byte in enumerate(data):
            result.append(byte ^ key_bytes[i % key_len])
        
        return bytes(result)

    def decrypt_data(self, encrypted_data: str) -> dict:
        """解密数据"""
        try:
            print(f"开始解密数据，长度: {len(encrypted_data)}")
            
            # 1. Base64解码
            decoded = base64.b64decode(encrypted_data)
            print(f"Base64解码后长度: {len(decoded)}")
            print(f"前50字节: {decoded[:50]}")
            
            # 2. 检查头部签名
            header_bytes = self.header_signature.encode('utf-8')
            print(f"期望的头部: {header_bytes}")
            print(f"实际的头部: {decoded[:len(header_bytes)]}")
            
            if not decoded.startswith(header_bytes):
                print("头部签名不匹配，尝试不同的解密方法...")
                # 可能头部不同，直接尝试XOR解密
                encrypted_payload = decoded
            else:
                encrypted_payload = decoded[len(header_bytes):]
            
            print(f"加密载荷长度: {len(encrypted_payload)}")
            
            # 3. XOR解密
            decrypted = self.xor_encrypt_decrypt(encrypted_payload, self.static_key)
            print(f"XOR解密后长度: {len(decrypted)}")
            print(f"解密后前100字节: {decrypted[:100]}")
            
            # 4. 尝试分离gzip数据和混淆数据
            print("尝试分离gzip数据...")
            
            # 方法1: 寻找gzip魔数
            gzip_magic = b'\x1f\x8b'
            gzip_start = decrypted.find(gzip_magic)
            if gzip_start >= 0:
                print(f"找到gzip魔数在位置: {gzip_start}")
                # 尝试从gzip魔数开始解压
                for end_pos in range(len(decrypted), gzip_start + 20, -1):
                    try:
                        gzip_data = decrypted[gzip_start:end_pos]
                        decompressed = gzip.decompress(gzip_data)
                        json_str = decompressed.decode('utf-8')
                        result = json.loads(json_str)
                        print(f"成功解密! gzip数据范围: {gzip_start}-{end_pos}")
                        return result
                    except Exception as e:
                        if end_pos % 50 == 0:  # 只打印部分错误信息
                            print(f"尝试位置 {end_pos} 失败: {str(e)[:50]}")
                        continue
            
            # 方法2: 尝试不同的分割点
            print("尝试不同的分割点...")
            for split_point in range(10, len(decrypted) - 50, 10):
                try:
                    gzip_data = decrypted[:split_point]
                    decompressed = gzip.decompress(gzip_data)
                    json_str = decompressed.decode('utf-8')
                    result = json.loads(json_str)
                    print(f"成功解密! 分割点: {split_point}")
                    return result
                except:
                    continue
            
            # 方法3: 从后往前尝试
            print("从后往前尝试...")
            for split_point in range(len(decrypted) - 100, 50, -10):
                try:
                    gzip_data = decrypted[:split_point]
                    decompressed = gzip.decompress(gzip_data)
                    json_str = decompressed.decode('utf-8')
                    result = json.loads(json_str)
                    print(f"成功解密! 分割点: {split_point}")
                    return result
                except:
                    continue
            
            # 方法4: 尝试整个数据作为gzip
            print("尝试整个数据作为gzip...")
            try:
                decompressed = gzip.decompress(decrypted)
                json_str = decompressed.decode('utf-8')
                result = json.loads(json_str)
                print("成功解密整个数据!")
                return result
            except Exception as e:
                print(f"整个数据解压失败: {e}")
            
            print("所有解密方法都失败了")
            return None
            
        except Exception as e:
            print(f"解密失败: {e}")
            return None

    def analyze_response(self, response_json):
        """分析响应数据"""
        print("分析响应数据...")
        print(f"响应JSON: {json.dumps(response_json, indent=2, ensure_ascii=False)}")
        
        if 'data' in response_json:
            encrypted_data = response_json['data']
            print(f"加密数据: {encrypted_data[:100]}...")
            
            # 解密数据
            decrypted = self.decrypt_data(encrypted_data)
            if decrypted:
                print("=" * 50)
                print("解密成功!")
                print("解密后的数据:")
                print(json.dumps(decrypted, indent=2, ensure_ascii=False))
                print("=" * 50)
                return decrypted
            else:
                print("解密失败")
                return None

if __name__ == "__main__":
    # 从之前的响应中获取的数据
    response_data = {
        "data": "LS0tLVRFQ0hfRVhBTV8yMDI0LS0tLUvua2hFeGFtMjNfZZQLrVNihWW/Pr0pmcHWdJr+Chxt91a+sEN/d1is3tLEh42DRD8DfYrSrIi8mIUmFBlRA+bTEaZO2eT9hZbEPoXQswPNTLUotNBSECXWLpLQNLcEV8blqZ8GGH2a8rFMHG/PkmDp+bicOB9qSAjEECnLdFKynrhoKzattNseV07MOl0XMc/HAeYUNaBnhuCtDWeRLG9ld/tRCRwFAGtZ3Pkj8Ok0HQk2iqfMicOZlju/4NKDZhMFkD44+oK0kcY6QKzDTGIniDRZA9mTzeO8qcd02gIRypZRVuReWlvLqpdDQ6byOQ8wwi6B0tWMDLTNsEIhlnLoD/9aSSYqJPh1LtYJo+NmxhmhXP29NX/8lzn3t8CgqkSZ0ypORPjsAzPVGOWqcpk+Sy5DQlc9JhUZDufyxnAsBFzse2MqSWArp9KOVMWyq8tRF90OkESnW9UA2DvfrolG+MyJy4cvvDgMy2JfpF/XoGInmxgT9lK3il3J4eaLAmxjciYvKyYtKB42Oh4SJj0aOjc1PS84CzpSX3FBVF4AAwUNaHNePEYAQhUGAB0uBEo2GSkzHyAsMCg6JgsgLSw=",
        "timestamp": 1749532379760,
        "status": "encrypted_response_sent",
        "encrypted": True
    }
    
    decryptor = ResponseDecryptor()
    result = decryptor.analyze_response(response_data)
    
    if result:
        print("\n最终解密结果:")
        print(json.dumps(result, indent=2, ensure_ascii=False))
    else:
        print("\n解密失败，需要进一步分析")
