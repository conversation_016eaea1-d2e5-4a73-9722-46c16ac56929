#!/usr/bin/env python3
"""
LinuxDo社区技术高考解决脚本
网络调试和数据解密能力测试
"""

import gzip
import json
import base64
import requests
from typing import Optional, Dict, Any
import time

class LinuxDoExamSolver:
    def __init__(self):
        self.base_url = "https://oldjs-gaokaolinuxdo.deno.dev"
        self.api_endpoint = f"{self.base_url}/api/exam"
        self.session = requests.Session()
        
        # 常见的可能密钥
        self.possible_keys = [
            "LinuxDo",
            "gaokao", 
            "exam",
            "tech",
            "challenge",
            "decrypt",
            "network",
            "debug",
            "oldjs",
            "deno"
        ]
        
        # 可能的头部标识
        self.possible_headers = [
            b"LDGK",  # LinuxDo GaoKao
            b"EXAM",
            b"TECH", 
            b"CHAL",
            b"\x1f\x8b",  # gzip magic number
            b"LD01",
            b"GK01"
        ]

    def xor_encrypt_decrypt(self, data: bytes, key: str) -> bytes:
        """XOR加密/解密函数"""
        key_bytes = key.encode('utf-8')
        key_len = len(key_bytes)
        result = bytearray()
        
        for i, byte in enumerate(data):
            result.append(byte ^ key_bytes[i % key_len])
        
        return bytes(result)

    def encrypt_data(self, data: Dict[Any, Any], key: str, header: bytes) -> bytes:
        """加密数据：JSON -> gzip压缩 -> XOR加密 -> 添加头部标识"""
        try:
            # 1. 转换为JSON字符串
            json_str = json.dumps(data, ensure_ascii=False)
            json_bytes = json_str.encode('utf-8')
            
            # 2. gzip压缩
            compressed = gzip.compress(json_bytes)
            
            # 3. XOR加密
            encrypted = self.xor_encrypt_decrypt(compressed, key)
            
            # 4. 添加头部标识
            final_data = header + encrypted
            
            return final_data
        except Exception as e:
            print(f"加密失败: {e}")
            return b""

    def decrypt_data(self, encrypted_data: bytes, key: str, header_len: int = 4) -> Optional[Dict]:
        """解密数据：移除头部 -> XOR解密 -> gzip解压 -> JSON解析"""
        try:
            # 1. 移除头部标识
            if len(encrypted_data) <= header_len:
                return None
            
            data_without_header = encrypted_data[header_len:]
            
            # 2. XOR解密
            decrypted = self.xor_encrypt_decrypt(data_without_header, key)
            
            # 3. gzip解压
            decompressed = gzip.decompress(decrypted)
            
            # 4. JSON解析
            json_str = decompressed.decode('utf-8')
            return json.loads(json_str)
            
        except Exception as e:
            print(f"解密失败 (key={key}, header_len={header_len}): {e}")
            return None

    def send_encrypted_request(self, data: Dict[Any, Any], key: str, header: bytes) -> Optional[bytes]:
        """发送加密请求"""
        try:
            encrypted_payload = self.encrypt_data(data, key, header)
            if not encrypted_payload:
                return None

            # 尝试不同的发送方式
            payloads_to_try = [
                # 方式1: base64编码
                {"encrypted_data": base64.b64encode(encrypted_payload).decode('ascii')},
                # 方式2: 直接发送二进制数据
                {"data": base64.b64encode(encrypted_payload).decode('ascii')},
                # 方式3: 十六进制编码
                {"encrypted_data": encrypted_payload.hex()},
                # 方式4: 直接发送原始数据
                {"payload": base64.b64encode(encrypted_payload).decode('ascii')}
            ]

            for i, payload in enumerate(payloads_to_try):
                print(f"尝试发送方式 {i+1}: {list(payload.keys())[0]}")

                response = self.session.post(
                    self.api_endpoint,
                    json=payload,
                    headers={
                        "Content-Type": "application/json",
                        "User-Agent": "LinuxDo-Exam-Solver/1.0"
                    },
                    timeout=30
                )

                print(f"请求状态: {response.status_code}")

                if response.status_code == 200:
                    return response.content
                elif response.status_code != 400:
                    print(f"响应: {response.text}")

            return None

        except Exception as e:
            print(f"发送请求失败: {e}")
            return None

    def try_decrypt_response(self, response_data: bytes) -> Optional[Dict]:
        """尝试解密响应数据"""
        print(f"响应数据长度: {len(response_data)}")
        print(f"响应数据前100字节: {response_data[:100]}")
        
        # 尝试不同的解密方法
        for key in self.possible_keys:
            for header_len in [0, 2, 4, 8]:
                result = self.decrypt_data(response_data, key, header_len)
                if result:
                    print(f"解密成功! 密钥: {key}, 头部长度: {header_len}")
                    return result
        
        # 尝试直接解析JSON
        try:
            return json.loads(response_data.decode('utf-8'))
        except:
            pass
        
        # 尝试base64解码后解析
        try:
            decoded = base64.b64decode(response_data)
            return json.loads(decoded.decode('utf-8'))
        except:
            pass
            
        return None

    def try_simple_requests(self):
        """尝试简单的请求方式"""
        print("尝试简单的请求方式...")

        simple_payloads = [
            {},
            {"action": "start"},
            {"start": True},
            {"exam": True},
            {"challenge": "start"},
            {"data": "test"}
        ]

        for payload in simple_payloads:
            print(f"尝试简单请求: {payload}")
            try:
                response = self.session.post(
                    self.api_endpoint,
                    json=payload,
                    headers={"Content-Type": "application/json"},
                    timeout=30
                )
                print(f"状态: {response.status_code}")
                if response.status_code != 400:
                    print(f"响应: {response.text}")
                    if response.status_code == 200:
                        return response.content
            except Exception as e:
                print(f"请求失败: {e}")

        return None

    def solve_exam(self):
        """解决考试挑战"""
        print("开始LinuxDo社区技术高考挑战...")

        # 首先尝试简单请求
        simple_result = self.try_simple_requests()
        if simple_result:
            print("简单请求成功!")
            return simple_result

        # 准备请求数据
        request_data = {
            "action": "start_exam",
            "timestamp": int(time.time()),
            "user_agent": "LinuxDo-Exam-Solver",
            "challenge_type": "network_debug_decrypt"
        }

        print(f"请求数据: {json.dumps(request_data, indent=2, ensure_ascii=False)}")

        # 只尝试前几个密钥和头部组合，避免太多请求
        for key in self.possible_keys[:3]:  # 只尝试前3个密钥
            for header in self.possible_headers[:3]:  # 只尝试前3个头部
                print(f"\n尝试密钥: {key}, 头部: {header}")

                response_data = self.send_encrypted_request(request_data, key, header)
                if response_data:
                    decrypted_response = self.try_decrypt_response(response_data)
                    if decrypted_response:
                        print("=" * 50)
                        print("挑战解决成功!")
                        print(f"使用的密钥: {key}")
                        print(f"使用的头部: {header}")
                        print("解密后的响应:")
                        print(json.dumps(decrypted_response, indent=2, ensure_ascii=False))
                        print("=" * 50)
                        return decrypted_response

        print("挑战解决失败，需要进一步分析")
        return None

if __name__ == "__main__":
    solver = LinuxDoExamSolver()
    result = solver.solve_exam()
    
    if result:
        print("\n最终结果:")
        print(json.dumps(result, indent=2, ensure_ascii=False))
    else:
        print("\n未能解决挑战，需要进一步分析")
