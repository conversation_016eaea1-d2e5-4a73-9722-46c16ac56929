function a(){const aT=['slice','charCodeAt','length','random','toString','concat','substring','ungzip','Data\x20decompression\x20error:','timeZone','【考试说明】请使用抓包软件（如Wireshark）或浏览器F12开发者工具查看网络请求，分析并解密传输数据，将解密结果填入下方答题区域','重新考试','🔍\x20或使用Wireshark等专业抓包工具分析网络流量','•\x20数据解密能力评估','•\x20协议分析实战操作','•\x20解密数据必须为有效的JSON格式','正确解密并提交请求数据和响应数据的JSON格式内容，所有必需字段完整且格式正确即可通过考试。通过考试后将获得LinuxDo社区颁发的网络调试与数据解密技能认证。','This\x20is\x20a\x20technical\x20ability\x20test\x20designed\x20to\x20assess\x20your\x20network\x20debugging\x20and\x20data\x20decryption\x20skills.\x20You\x20need\x20to\x20use\x20technical\x20means\x20to\x20obtain\x20and\x20decrypt\x20network\x20transmitted\x20data.','This\x20exam\x20tests\x20your\x20Next.js\x20project\x20reverse\x20analysis\x20capabilities,\x20network\x20protocol\x20understanding,\x20encryption\x20algorithm\x20reverse\x20engineering\x20skills,\x20and\x20data\x20decryption\x20&\x20restoration\x20abilities','Response\x20data\x20decryption\x20result\x20(JSON\x20format):','Please\x20enter\x20the\x20decrypted\x20request\x20data\x20in\x20JSON\x20format:','Request\x20data\x20decryption\x20result\x20(JSON\x20format):','提交答案','开始考试','LinuxDo社区高考','网络调试与数据解密能力考试','📋\x20考试范围\x20|\x20Exam\x20Scope','本次考试考验您的Next.js项目逆向分析能力、网络协议理解能力、加密算法逆向工程技能以及数据解密与还原能力','欢迎参加LinuxDo社区技术高考！本次考试将全面检验您的网络调试和数据解密能力。考试采用实战模式，需要您运用专业技术手段获取并解密网络传输数据。','📋\x20考试须知','📝\x20考试科目','•\x20网络调试技能测试','⏰\x20考试时间','•\x20不限时间，可反复尝试','•\x20建议预留30-60分钟','•\x20支持中途暂停和继续','🛠️\x20必备工具','•\x20浏览器F12开发者工具','•\x20Wireshark抓包软件（可选）','•\x20JSON格式化工具（可选）','🎯\x20评分标准','•\x20数据解密准确性：50%','•\x20JSON格式正确性：30%','•\x20字段完整性：20%','⚠️\x20重要提醒','•\x20请确保网络连接稳定','•\x20考试过程中请勿刷新页面','•\x20所有字段名和值都区分大小写','•\x20时间戳等动态字段需要使用实际值','🏆\x20通过标准','🔐\x20密钥刷新倒计时','距离下次密钥刷新还有','小时',':','分钟','秒','系统每6小时自动刷新加密密钥，确保数据传输安全性。所有用户的密钥刷新时间完全同步。','考试技巧与提示','📋\x20使用浏览器F12开发者工具的Network标签页查看网络请求','🔐\x20数据经过了gzip压缩\x20+\x20XOR加密\x20+\x20头部标识等多重保护','🧠\x20需要理解加密算法并正确解密数据','✅\x20解密后的数据应该是有效的JSON格式','LinuxDo社区高考','中文','English','专业的技术能力评估平台','LinuxDo社区技术高考是一个专业的网络技术能力测试平台，专注于评估参与者的网络调试和数据解密技能。\x20我们的考试采用实战模式，要求考生运用专业的技术手段，如Wireshark抓包分析、浏览器F12开发者工具等，\x20来获取并解密网络传输的数据。','考试特色','实战导向：真实的网络环境和加密数据','技能全面：涵盖网络调试、数据解密、协议分析','难度适中：适合有技术基础的程序员和工程师','即时反馈：自动化评分系统，即时获得结果','适用人群','程序员：提升网络调试和安全技能','网络工程师：验证网络协议分析能力','安全研究员：测试数据解密和逆向分析技能','运维工程师：增强网络故障排查能力','技术学生：学习和实践网络安全知识','技术要求','熟悉HTTP/HTTPS协议','了解网络抓包工具使用','掌握基本的加密解密概念','具备JSON数据格式处理能力','理解网络请求和响应流程','考试流程','点击开始考试按钮','系统生成加密的网络请求','使用工具捕获和分析网络数据','解密请求和响应数据','提交解密结果进行验证','获得考试结果和技能评估','相关关键词','网络调试,\x20数据解密,\x20抓包分析,\x20Wireshark,\x20F12调试,\x20网络安全,\x20技术测试,\x20程序员考试,\x20加密解密,\x20协议分析,\x20LinuxDo,\x20社区高考,\x20技能认证,\x20网络工程师,\x20安全研究,\x20运维技能,\x20JSON解析,\x20HTTP协议,\x20HTTPS加密','LinuxDo社区高考\x20-\x20网络调试和数据解密能力测试','gzip','compress','decompress','inflate','deflate','pako','zlib','ungzip','xor','encrypt','decrypt','challenge','exam','api','post','fetch','LinuxDo','header','LDGK','EXAM','TECH','CHAL','LD01','GK01','gaokao','tech','network','debug','oldjs','deno'];
