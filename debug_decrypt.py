#!/usr/bin/env python3
"""
调试解密过程
"""

import gzip
import json
import base64

def xor_decrypt(data: bytes, key: str) -> bytes:
    """XOR解密"""
    key_bytes = key.encode('utf-8')
    key_len = len(key_bytes)
    result = bytearray()
    
    for i, byte in enumerate(data):
        result.append(byte ^ key_bytes[i % key_len])
    
    return bytes(result)

def debug_decrypt():
    """调试解密过程"""
    encrypted_data = "LS0tLVRFQ0hfRVhBTV8yMDI0LS0tLUvua2hFeGFtMjNfZZQLrVNihWW/Pr0pmcHWdJr+Chxt91a+sEN/d1is3tLEh42DRD8DfYrSrIi8mIUmFBlRA+bTEaZO2eT9hZbEPoXQswPNTLUotNBSECXWLpLQNLcEV8blqZ8GGH2a8rFMHG/PkmDp+bicOB9qSAjEECnLdFKynrhoKzattNseV07MOl0XMc/HAeYUNaBnhuCtDWeRLG9ld/tRCRwFAGtZ3Pkj8Ok0HQk2iqfMicOZlju/4NKDZhMFkD44+oK0kcY6QKzDTGIniDRZA9mTzeO8qcd02gIRypZRVuReWlvLqpdDQ6byOQ8wwi6B0tWMDLTNsEIhlnLoD/9aSSYqJPh1LtYJo+NmxhmhXP29NX/8lzn3t8CgqkSZ0ypORPjsAzPVGOWqcpk+Sy5DQlc9JhUZDufyxnAsBFzse2MqSWArp9KOVMWyq8tRF90OkESnW9UA2DvfrolG+MyJy4cvvDgMy2JfpF/XoGInmxgT9lK3il3J4eaLAmxjciYvKyYtKB42Oh4SJj0aOjc1PS84CzpSX3FBVF4AAwUNaHNePEYAQhUGAB0uBEo2GSkzHyAsMCg6JgsgLSw="
    
    static_key = "TechExam2024_EncryptionKey_ForTesting"
    header_signature = "----TECH_EXAM_2024----"
    
    print("开始调试解密过程...")
    
    # 1. Base64解码
    decoded = base64.b64decode(encrypted_data)
    print(f"Base64解码后长度: {len(decoded)}")
    print(f"前20字节: {decoded[:20]}")
    
    # 2. 移除头部
    header_bytes = header_signature.encode('utf-8')
    payload = decoded[len(header_bytes):]
    print(f"移除头部后长度: {len(payload)}")
    
    # 3. XOR解密
    decrypted = xor_decrypt(payload, static_key)
    print(f"XOR解密后长度: {len(decrypted)}")
    print(f"解密后前20字节: {decrypted[:20]}")
    print(f"解密后前20字节(hex): {decrypted[:20].hex()}")
    
    # 4. 分析gzip结构
    if decrypted.startswith(b'\x1f\x8b'):
        print("确认是gzip格式")
        
        # 分析gzip头部
        print(f"gzip方法: {decrypted[2]}")
        print(f"gzip标志: {decrypted[3]}")
        
        # 尝试找到gzip数据的结束位置
        # gzip通常以特定的CRC和大小结尾
        print("尝试不同的gzip数据长度...")
        
        for i in range(100, len(decrypted), 10):
            try:
                gzip_data = decrypted[:i]
                decompressed = gzip.decompress(gzip_data)
                print(f"长度 {i} 成功解压，解压后长度: {len(decompressed)}")
                
                try:
                    json_str = decompressed.decode('utf-8')
                    result = json.loads(json_str)
                    print(f"成功解析JSON!")
                    print("解密结果:")
                    print(json.dumps(result, indent=2, ensure_ascii=False))
                    return result
                except Exception as e:
                    print(f"JSON解析失败: {e}")
                    print(f"解压内容: {decompressed[:100]}")
                    
            except Exception as e:
                if i % 50 == 0:
                    print(f"长度 {i} 解压失败: {str(e)[:30]}")
                continue
    
    print("所有尝试都失败了")
    
    # 尝试保存原始数据用于进一步分析
    with open("decrypted_raw.bin", "wb") as f:
        f.write(decrypted)
    print("原始解密数据已保存到 decrypted_raw.bin")
    
    return None

if __name__ == "__main__":
    debug_decrypt()
