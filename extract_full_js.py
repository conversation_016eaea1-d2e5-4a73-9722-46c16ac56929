#!/usr/bin/env python3
"""
提取完整的JavaScript内容并分析加密逻辑
"""

import requests
import re
import json

def get_full_js_content():
    """获取完整的JavaScript内容"""
    url = "https://oldjs-gaokaolinuxdo.deno.dev/_next/static/chunks/app/page-a806755edcfd0c21.js"
    
    response = requests.get(url)
    if response.status_code == 200:
        return response.text
    return None

def analyze_encryption_logic(js_content):
    """分析加密逻辑"""
    print("分析加密逻辑...")
    
    # 从JavaScript内容中我们可以看到一些关键信息
    # 从数组中可以看到这些关键词：
    keywords_found = [
        'gzip', 'compress', 'decompress', 'inflate', 'deflate', 'pako', 'zlib', 'ungzip',
        'xor', 'encrypt', 'decrypt', 'challenge', 'exam', 'api', 'post', 'fetch',
        'LinuxDo', 'header', 'LDGK', 'EXAM', 'TECH', 'CHAL', 'LD01', 'GK01',
        'gaokao', 'tech', 'network', 'debug', 'oldjs', 'deno'
    ]
    
    print("发现的关键词:")
    for keyword in keywords_found:
        print(f"  - {keyword}")
    
    # 查找可能的加密函数
    # 寻找XOR加密函数
    xor_patterns = [
        r'function\s+(\w*xor\w*)\s*\([^)]*\)\s*{[^}]*}',
        r'(\w*xor\w*)\s*[:=]\s*function\s*\([^)]*\)\s*{[^}]*}',
        r'function[^{]*{[^}]*xor[^}]*}',
    ]
    
    for pattern in xor_patterns:
        matches = re.findall(pattern, js_content, re.IGNORECASE | re.DOTALL)
        if matches:
            print(f"找到XOR相关函数: {matches}")
    
    # 查找gzip相关函数
    gzip_patterns = [
        r'function\s+(\w*gzip\w*)\s*\([^)]*\)\s*{[^}]*}',
        r'(\w*gzip\w*)\s*[:=]\s*function\s*\([^)]*\)\s*{[^}]*}',
        r'function[^{]*{[^}]*gzip[^}]*}',
    ]
    
    for pattern in gzip_patterns:
        matches = re.findall(pattern, js_content, re.IGNORECASE | re.DOTALL)
        if matches:
            print(f"找到gzip相关函数: {matches}")

def extract_potential_keys():
    """从发现的关键词中提取潜在密钥"""
    potential_keys = [
        'LinuxDo', 'gaokao', 'tech', 'network', 'debug', 'oldjs', 'deno',
        'exam', 'challenge', 'LDGK', 'EXAM', 'TECH', 'CHAL', 'LD01', 'GK01'
    ]
    
    potential_headers = [
        b'LDGK', b'EXAM', b'TECH', b'CHAL', b'LD01', b'GK01',
        b'LinuxDo', b'gaokao', b'oldjs', b'deno'
    ]
    
    return potential_keys, potential_headers

def create_improved_solver():
    """创建改进的解决方案"""
    keys, headers = extract_potential_keys()
    
    print("潜在密钥:")
    for key in keys:
        print(f"  - {key}")
    
    print("潜在头部:")
    for header in headers:
        print(f"  - {header}")
    
    # 基于发现的信息，我们知道：
    # 1. 使用了gzip压缩
    # 2. 使用了XOR加密
    # 3. 有头部标识
    # 4. 可能的密钥和头部已经在JavaScript中暴露
    
    return keys, headers

if __name__ == "__main__":
    print("开始分析JavaScript文件...")
    
    js_content = get_full_js_content()
    if js_content:
        print(f"获取到JavaScript内容，长度: {len(js_content)}")
        analyze_encryption_logic(js_content)
        
        # 保存完整内容到文件
        with open("full_page_js.js", "w", encoding="utf-8") as f:
            f.write(js_content)
        print("完整JavaScript内容已保存到 full_page_js.js")
    
    keys, headers = create_improved_solver()
    
    print("\n基于分析的建议:")
    print("1. 使用发现的密钥和头部组合")
    print("2. 确保gzip压缩 -> XOR加密 -> 添加头部的顺序")
    print("3. 可能需要特定的请求格式")
