import { columnSearch, sorteColum } from '@/utils/columnSearch';
import { computeWeight, computeWeights } from '@/utils/query';
import TipTitle from '@/components/TipTitle';
import { intransitRemark } from '@/pages/VehicleMonitoringNew/Tables/titleRemark';

export default function Columns(
  // isExport,
  tableColumnsType,
) {
  return [
    {
      title: '路由名称',
      dataIndex: 'routePath',
      align: 'center',
      ellipsis: true,
      fixed: 'left',
      width: 60,
      ...columnSearch('routePath'),
    },
    // ...(isExport ? [
    //   {
    //     title: '始发分拨',
    //     dataIndex: 'originCenter2',
    //           align: 'center',
    // ellipsis: true,
    //     fixed: 'left',
    //     width: 120,
    //     ...columnSearch('originCenter2'),
    //   },
    //   {
    //     title: '目的分拨',
    //     dataIndex: 'destinationCenter2',
    //           align: 'center',
    // ellipsis: true,
    //     fixed: 'left',
    //     width: 120,
    //     ...columnSearch('destinationCenter2')
    //   },

    // ] : []),
    {
      title: '始发营运区',
      dataIndex: 'originProvinceArea',
      align: 'center',
      ellipsis: true,
      fixed: 'left',
      width: 60,
      ...columnSearch('originProvinceArea'),
    },
    {
      title: '目地营运区',
      dataIndex: 'destinationProvinceArea',
      align: 'center',
      ellipsis: true,
      fixed: 'left',
      width: 60,
      ...columnSearch('destinationProvinceArea'),
    },
    {
      title: '总货量(T)',
      dataIndex: 'sectionOfAllCalcWeight',
      align: 'center',
      ellipsis: true,
      width: 40,
      sorter: sorteColum('sectionOfAllCalcWeight'),
    },
    // {
    //   title: '<=300kg(T)',
    //   dataIndex: 'weight1',
    //   align: 'center',
    //   ellipsis: true,
    //   width: 120,
    //   sorter: sorteColum('weight1'),
    // },
    // {
    //   title: '<=300kg票数',
    //   dataIndex: 'weight2',
    //   align: 'center',
    //   ellipsis: true,
    //   width: 140,
    //   sorter: sorteColum('weight2'),
    // },
    // {
    //   title: '>300kg(T)',
    //   dataIndex: 'weight3',
    //   align: 'center',
    //   ellipsis: true,
    //   width: 120,
    //   sorter: sorteColum('weight3'),
    // },
    // {
    //   title: '>300kg票数',
    //   dataIndex: 'weight4',
    //   align: 'center',
    //   ellipsis: true,
    //   width: 120,
    //   sorter: sorteColum('weight4'),
    // },
    ...(tableColumnsType === 'product'
      ? [
          // {
          //   title: '填仓',
          //   dataIndex: 'weight12',
          //   align: 'center',
          //   ellipsis: true,
          //   sorter: sorteColum('weight12'),
          // },
          // {
          //   title: '整车',
          //   dataIndex: 'sectionOfZCCalcWeight',
          //   align: 'center',
          //   ellipsis: true,
          //   width: 40,
          //   sorter: sorteColum('sectionOfZCCalcWeight'),
          // },
          // {
          //   title: '定时达',
          //   dataIndex: 'sectionOfDSDCalcWeight',
          //   align: 'center',
          //   ellipsis: true,
          //   width: 40,
          //   sorter: sorteColum('sectionOfDSDCalcWeight'),
          // },
          // {
          //   title: '安心达',
          //   dataIndex: 'sectionOfAXDCalcWeight',
          //   align: 'center',
          //   ellipsis: true,
          //   width: 40,
          //   sorter: sorteColum('sectionOfAXDCalcWeight'),
          // },
          {
            title: 'MINI小件',
            dataIndex: 'sectionOfMINIXJCalcWeight',
            align: 'center',
            ellipsis: true,
            width: 40,
            // render: (v) => computeWeight(v),
            sorter: sorteColum('sectionOfMINIXJCalcWeight'),
          },
          {
            title: 'MINI大件',
            dataIndex: 'sectionOfMINIDJCalcWeight',
            width: 40,
            align: 'center',
            ellipsis: true,
            // render: (v) => computeWeight(v),
            sorter: sorteColum('sectionOfMINIDJCalcWeight'),
          },
          // {
          //   title: '普惠达',
          //   dataIndex: 'weight5',
          //   align: 'center',
          //   ellipsis: true,
          //   // render: (v) => computeWeight(v),
          //   sorter: sorteColum('weight5'),
          // },
          {
            title: '精准零担',
            dataIndex: 'sectionOfJZLDCalcWeight',
            align: 'center',
            ellipsis: true,
            width: 40,
            // render: (v) => computeWeight(v),
            sorter: sorteColum('sectionOfJZLDCalcWeight'),
          },
          {
            remak: '时效类型不是隔日达和次日达',
            title: <TipTitle title="其他" tipContent='安心达+定时达'>其他</TipTitle>,
            dataIndex: 'sectionOfOtherCalcWeight',
            width: 40,
            align: 'center',
            ellipsis: true,
            // render: (v) => computeWeight(v),
            sorter: sorteColum('sectionOfOtherCalcWeight'),
          },
        ]
      : [
          {
            title: '0-35',
            dataIndex: 'sectionOf0To35kgCalcWeight',
            align: 'center',
            ellipsis: true,
            width: 40,
            sorter: sorteColum('sectionOf0To35kgCalcWeight'),
          },
          {
            title: '35-70',
            dataIndex: 'sectionOf35To70kgCalcWeight',
            align: 'center',
            ellipsis: true,
            width: 40,
            sorter: sorteColum('sectionOf35To70kgCalcWeight'),
          },
          {
            title: '70-300',
            dataIndex: 'sectionOf70To300kgCalcWeight',
            align: 'center',
            ellipsis: true,
            width: 40,
            sorter: sorteColum('sectionOf70To300kgCalcWeight'),
          },
          {
            title: '300-800',
            dataIndex: 'sectionOf300To800kgCalcWeight',
            align: 'center',
            ellipsis: true,
            width: 40,
            sorter: sorteColum('sectionOf300To800kgCalcWeight'),
          },
          {
            title: '800+',
            dataIndex: 'sectionOfGreaterThan800kgCalcWeight',
            align: 'center',
            ellipsis: true,
            width: 40,
            sorter: sorteColum('sectionOfGreaterThan800kgCalcWeight'),
          },
          // {
          //   title: '其他',
          //   dataIndex: 'sectionOfOtherCalcWeight',
          //   width: 40,
          //   align: 'center',
          //   ellipsis: true,
          //   // render: (v) => computeWeight(v),
          //   sorter: sorteColum('sectionOfOtherCalcWeight'),
          // },
        ]),
    // {
    //   title: '次日达',
    //   dataIndex: 'weight7',
    //   align: 'center',
    //   ellipsis: true,
    //   // render: (v) => computeWeight(v),
    //   sorter: sorteColum('weight7'),
    // },
    // {
    //   title: '隔日达',
    //   dataIndex: 'weight8',
    //   align: 'center',
    //   ellipsis: true,
    //   // render: (v) => computeWeight(v),
    //   sorter: sorteColum('weight8'),
    // },
    // {
    //   title: '800KG+',
    //   dataIndex: 'weight9',
    //   align: 'center',
    //   ellipsis: true,
    //   // render: (v) => computeWeight(v),
    //   sorter: sorteColum('weight9'),
    // }
  ];
}
