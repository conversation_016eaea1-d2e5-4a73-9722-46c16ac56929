import { Col, Row, Select, Table } from 'antd';
import React, { useEffect, useState } from 'react';

import { ButtonGroupPro, BizExport } from '@aned/bizcomponent';
import Columns from './Columns';
import { ExportExcel } from '@/utils/exportXLSX';
import { ExportOutlined } from '@ant-design/icons';
import { computeWeights } from '@/utils/query';
import { connect } from 'react-redux';
import styles from './index.less';
import useFirstTimeVisit from '@/hooks/useFirstTimeVisit/index';

const { Option } = Select;

function TableList(props) {
  const {
    odgoodsweightinfosr: {
      totalRow,
      searchParams: filter,
      tableColumns,
      oDGoodsWeight,
      oDGoodsWeightTotal,
      tableColumnsType,
    },
    loading,
    dispatch,
  } = props;
  const isFirstVisit = useFirstTimeVisit();
  const [exportVisible, setExportVisible] = useState(false);
  useEffect(() => {
    handleChange('product');
  }, []);
  const [pageSize, setPagesize] = useState(10);
  const [columnsType, setColumnsType] = useState('product');
  function handleChange(value) {
    setColumnsType(value);
    dispatch({
      type: 'odgoodsweightinfosr/updateState',
      payload: {
        tableColumnsType: value,
        oDGoodsWeight: [],
        oDGoodsWeightTotal: {},
        totalRow: [],
      },
    });
    if (!isFirstVisit)
      dispatch({
        type: 'odgoodsweightinfosr/getDatas',
      });
  }
  const exportXlsx = () => {
    ExportExcel(
      Columns(columnsType),
      oDGoodsWeight,
      `路由货量明细 ${filter.startDate}到${filter.endDate}-${
        tableColumnsType === 'product' ? '产品类型' : '公斤段'
      }`,
      `路由货量明细-${tableColumnsType === 'product' ? '产品类型' : '公斤段'}`,
    );
  };
  const exportData = (columns) => {
    const { auth, ...rest } = filter;
    dispatch({
      type: 'odgoodsweightinfo/exportData',
      payload: {
        ...rest,
        dataType: 1,
        columns,
      },
      callback: () => {
        setExportVisible(false);
      },
    });
  };
  return (
    <div className="table-wrap-ane">
      <BizExport
        destroyOnClose={true}
        visible={exportVisible}
        columns={Columns(columnsType)}
        handleCancel={() => setExportVisible(false)}
        onExport={(columns) => exportData(columns)}
        unkey="dataIndex"
        templateKey="odGoodsWeightInfoSR"
        formatSplicing={(item) => `t|${item.dataIndex}`}
      />
      <Row>
        <Col span={20}>
          <ButtonGroupPro
            button={[
              {
                label: '导出',
                path: '/route/od/ODGoodsWeight/exportLocal',
                icon: <ExportOutlined />,
                onClick: exportXlsx,
              },
              {
                path: '/route/od/ODGoodsWeight/export',
                label: '导出',
                icon: <ExportOutlined />,
                onClick: () => setExportVisible(true),
              },
            ]}
          />
        </Col>
        <Col span={4}>
          {tableColumns.length && (
            <Select
              defaultValue={columnsType}
              style={{ width: 140, float: 'right', marginRight: '20px', marginTop: '15px' }}
              onChange={handleChange}
            >
              {tableColumns.map((column) => (
                <Option key={column.value} value={column.value}>
                  {column.label}
                </Option>
              ))}
            </Select>
          )}
        </Col>
      </Row>

      <Table
        loading={loading}
        showSorterTooltip={false}
        rowKey="index"
        bordered
        columns={Columns(columnsType)}
        dataSource={oDGoodsWeight}
        // scroll={{ x: 2000 }}
        // eslint-disable-next-line no-unused-vars
        onChange={(pagination, filters, sorter) => {
          setPagesize(pagination.pageSize);
        }}
        summary={() => {
          const renderProductSummaryCells = () => (
            <>
              <Table.Summary.Cell align="center">
                {oDGoodsWeightTotal?.sectionOfAllCalcWeight}
              </Table.Summary.Cell>
              {/*<Table.Summary.Cell align="center">*/}
              {/*  {oDGoodsWeightTotal?.sectionOfZCCalcWeight}*/}
              {/*</Table.Summary.Cell>*/}
              {/*<Table.Summary.Cell align="center">*/}
              {/*  {oDGoodsWeightTotal?.sectionOfDSDCalcWeight}*/}
              {/*</Table.Summary.Cell>*/}
              {/*<Table.Summary.Cell align="center">*/}
              {/*  {oDGoodsWeightTotal?.sectionOfAXDCalcWeight}*/}
              {/*</Table.Summary.Cell>*/}
              <Table.Summary.Cell align="center">
                {oDGoodsWeightTotal?.sectionOfMINIXJCalcWeight}
              </Table.Summary.Cell>
              <Table.Summary.Cell align="center">
                {oDGoodsWeightTotal?.sectionOfMINIDJCalcWeight}
              </Table.Summary.Cell>
              <Table.Summary.Cell align="center">
                {oDGoodsWeightTotal?.sectionOfJZLDCalcWeight}
              </Table.Summary.Cell>
              <Table.Summary.Cell align="center">
                {oDGoodsWeightTotal?.sectionOfOtherCalcWeight}
              </Table.Summary.Cell>
            </>
          );
          const renderODGoodsWeightTotalCells = () => (
            <>
              <Table.Summary.Cell align="center">
                {oDGoodsWeightTotal?.sectionOfAllCalcWeight}
              </Table.Summary.Cell>
              <Table.Summary.Cell align="center">
                {oDGoodsWeightTotal?.sectionOf0To35kgCalcWeight}
              </Table.Summary.Cell>
              <Table.Summary.Cell align="center">
                {oDGoodsWeightTotal?.sectionOf35To70kgCalcWeight}
              </Table.Summary.Cell>
              <Table.Summary.Cell align="center">
                {oDGoodsWeightTotal?.sectionOf70To300kgCalcWeight}
              </Table.Summary.Cell>
              <Table.Summary.Cell align="center">
                {oDGoodsWeightTotal?.sectionOf300To800kgCalcWeight}
              </Table.Summary.Cell>
              <Table.Summary.Cell align="center">
                {oDGoodsWeightTotal?.sectionOfGreaterThan800kgCalcWeight}
              </Table.Summary.Cell>
              {/*<Table.Summary.Cell align="center">*/}
              {/*  {oDGoodsWeightTotal?.sectionOfOtherCalcWeight}*/}
              {/*</Table.Summary.Cell>*/}
            </>
          );

          return (
            <>
              <Table.Summary.Row>
                <Table.Summary.Cell className="flow-cell-sticky" colSpan={3}>
                  合计
                </Table.Summary.Cell>
                {tableColumnsType === 'product'
                  ? renderProductSummaryCells()
                  : renderODGoodsWeightTotalCells()}
              </Table.Summary.Row>
            </>
          );
        }}
        pagination={{
          size: 'small',
          pageSize: pageSize,
          showTotal: () => `总共：${oDGoodsWeight.length}条`,
        }}
      />
    </div>
  );
}

export default connect((state) => ({
  odgoodsweightinfosr: state.odgoodsweightinfosr,
  loading: state.loading.models.odgoodsweightinfosr,
}))(TableList);
