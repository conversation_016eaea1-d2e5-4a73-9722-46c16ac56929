/**
 * 在生产环境 代理是无法生效的，所以这里没有生产环境的配置
 */

// 定义环境相关的目标URL
const targets = {
  test: 'http://10.10.106.125:10086',
  sat: 'http://122.144.136.163:16881',
  prod: 'https://eagle.ane56.com',
};

const DEBUG_ENV = process.env?.DEBUG_ENV;
// 设置默认目标为测试环境
const defaultTarget = targets['sat'];

// 定义通用的代理配置
const commonProxy = {
  changeOrigin: true,
};

// 定义路由重写规则
const pathRewrites = {
  basic: '/route/basic',
  temporary: '/route/temporary',
  interface: '/route/interface',
  maxcenterbusi: '/route/maxcenterbusi',
  maxcenter: '/route/maxcenter',
  'file-system': '/route/file-system',
  'mdp-station-keyword': '/route/mdp-station-keyword',
  'mdp-station-organization': '/route/mdp-station-organization',
  'mdp-station-detail': '/route/mdp-station-detail',
  'mdp-micro-station': '/route/mdp-micro-station',
  'mdp-micro-other-station': '/route/mdp-micro-other-station',
  'mdp-micro-district': '/route/mdp-micro-district',
  'tdcs-dispatch': '/tdcs/dispatch',
  'mdp-district': '/route/mdp-district',
  route: '/route',
};

// 创建代理配置的辅助函数
const createProxy = (targetUrl, pathRewrite) => ({
  ...commonProxy,
  target: targetUrl,
  pathRewrite: { '^': pathRewrites[pathRewrite] || pathRewrite },
});

// 定义API路径和对应的重写规则
const apiConfigs = {
  '/api/transfers/selectNumberOfTransfersList': 'basic',
  '/api/transfers/selectTransitFrequencyList': 'basic',
  '/api/route/hbase/getDeliveryReceiptMessage': 'route',
  '/api/route/exportTask/': 'basic',
  '/api/adjust/': 'temporary',
  '/api/routeTemporary/': 'temporary',
  '/api/routeAlterFlow/': 'temporary',
  '/api/tms/': 'interface',
  '/api/route/trunkLineController/': 'basic',
  '/api/v1/track/receipt/infoDetail': 'maxcenterbusi',
  '/api/v1/': 'maxcenterbusi',
  '/api/noencrypt/': 'maxcenter',
  '/api/fileSystem/': 'file-system',
  '/api/station/keyword/': 'mdp-station-keyword',
  '/api/station/organization/': 'mdp-station-organization',
  '/api/station/organizationInfo/': 'mdp-station-detail',
  '/api/micro/station/': 'mdp-micro-station',
  '/api/micro/other/station/': 'mdp-micro-other-station',
  '/api/micro/district': 'mdp-micro-district',
  '/api/integration': 'mdp-micro-district',
  '/api/dispatchTaskCost/': 'tdcs-dispatch',
  '/api/fullRateManage/': 'tdcs-dispatch',
  '/api/route/queryForecastBySiteDepartureList': 'route',
  '/api/station/detail/selectByCodes': 'mdp-station-detail',
  '/api/station': 'mdp-station-business',
  '/api/route/od/provinceCargoVolumeMatrix/select': 'route',
  '/api/district/selectProvince': 'mdp-district',
  '/api/imputation': 'route',
  '/api/cashing': 'basic',
  '/api/sign/offline': 'basic',
  '/api/static/cashing/rate': 'basic',
  '/api/transfers': 'basic',
  '/api/getSitePlan': 'route',
  '/api/getTrunkLinePlan': 'route',
  '/api/getRepertory': 'route',
  '/api/departWeightDisperse': 'route',
  '/api/aging': 'basic',
  '/api/district': 'mdp-district',
  '/api/adjust': 'temporary',
  '/api/': 'route',
  '/api/departWeightDisperse': 'route',
  '/api/exportTask': 'foreign',
};

// 特殊配置
const specialConfigs = {
  '/api/dictionary': 'http://10.10.106.25:6771',
  // '/api/adjust': 'http://10.10.106.144:8088',
  // '/api/adjust/isRestrictedAllocation': ['http://10.10.106.144:8088', 'temporary'],
};

// 生成代理配置
const generateProxyConfig = (configs, defaultTarget) => {
  return Object.entries(configs).reduce((acc, [path, rewrite]) => {
    if (Array.isArray(rewrite)) {
      acc[path] = createProxy(...rewrite);
    } else if (typeof rewrite === 'string' && rewrite.startsWith('http')) {
      acc[path] = createProxy(rewrite);
    } else {
      acc[path] = createProxy(defaultTarget, rewrite);
    }
    return acc;
  }, {});
};

export default {
  dev: {
    ...generateProxyConfig(apiConfigs, defaultTarget),
    ...generateProxyConfig(specialConfigs, defaultTarget),
  },
  test: {},
  pre: {},
};
